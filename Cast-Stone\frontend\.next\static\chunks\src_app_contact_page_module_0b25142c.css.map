{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/contact/page.module.css"], "sourcesContent": ["/* Contact page specific styles */\n.container {\n  min-height: 100vh;\n  padding: 2rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.main {\n  max-width: 600px;\n  width: 100%;\n  background: white;\n  border-radius: 12px;\n  padding: 3rem;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n.title {\n  font-size: 2.5rem;\n  font-weight: bold;\n  text-align: center;\n  margin-bottom: 1rem;\n  color: #1f2937;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.description {\n  text-align: center;\n  color: #6b7280;\n  margin-bottom: 2rem;\n  font-size: 1.125rem;\n}\n\n.form {\n  margin-bottom: 3rem;\n}\n\n.formGroup {\n  margin-bottom: 1.5rem;\n}\n\n.label {\n  display: block;\n  font-weight: 500;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n\n.input,\n.textarea {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.2s, box-shadow 0.2s;\n  box-sizing: border-box;\n}\n\n.input:focus,\n.textarea:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.textarea {\n  resize: vertical;\n  min-height: 120px;\n}\n\n.submitButton {\n  width: 100%;\n  padding: 0.875rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.submitButton:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n}\n\n.contactInfo {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n  padding: 2rem;\n  background: #f9fafb;\n  border-radius: 8px;\n}\n\n@media (min-width: 640px) {\n  .contactInfo {\n    grid-template-columns: 1fr 1fr;\n  }\n}\n\n.infoItem {\n  text-align: center;\n}\n\n.infoTitle {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 0.5rem;\n}\n\n.infoText {\n  color: #6b7280;\n}\n\n.backLink {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  text-decoration: none;\n  border-radius: 6px;\n  font-weight: 500;\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.backLink:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAWA;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;AAUA;EACE;;;;;AAKF;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAYA", "debugId": null}}]}