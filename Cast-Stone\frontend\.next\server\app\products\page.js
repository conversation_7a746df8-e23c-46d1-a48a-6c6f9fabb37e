(()=>{var e={};e.id=571,e.ids=[571],e.modules={319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},474:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var a=r(1261),n=r.n(a)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return o}});let a=r(4985),n=r(4953),i=r(6533),s=a._(r(1933));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=i.Image},2295:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},2384:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var a=r(5239),n=r(8088),i=r(8170),s=r.n(i),o=r(893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8547)),"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\products\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\products\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>c});var a=r(7413),n=r(2376),i=r.n(n),s=r(8726),o=r.n(s);r(1135);let c={title:"Create Next App",description:"Generated by create next app"};function l({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:e})})}},5613:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(687),n=r(3210),i=r(474),s=r(7404),o=r.n(s);function c(){let[e,t]=(0,n.useState)("all"),r=[{id:1,name:"Classic Fireplace Mantel",category:"fireplaces",price:"$2,500",image:"/images/fireplace-collection.jpg",description:"Handcrafted traditional mantel with intricate detailing"},{id:2,name:"Modern Fireplace Surround",category:"fireplaces",price:"$3,200",image:"/images/fireplace-collection.jpg",description:"Contemporary design with clean lines and elegant finish"},{id:3,name:"Garden Fountain",category:"garden",price:"$1,800",image:"/images/garden-collection.jpg",description:"Three-tier fountain perfect for outdoor spaces"},{id:4,name:"Decorative Planters",category:"garden",price:"$450",image:"/images/garden-collection.jpg",description:"Set of elegant planters for garden landscaping"},{id:5,name:"Classical Columns",category:"architectural",price:"$1,200",image:"/images/architectural-collection.jpg",description:"Corinthian style columns for grand entrances"},{id:6,name:"Decorative Balustrade",category:"architectural",price:"$800",image:"/images/architectural-collection.jpg",description:"Ornate balustrade for staircases and terraces"},{id:7,name:"Wall Medallions",category:"decorative",price:"$350",image:"/images/architectural-collection.jpg",description:"Decorative wall medallions for interior accent"},{id:8,name:"Ornamental Corbels",category:"decorative",price:"$280",image:"/images/architectural-collection.jpg",description:"Supporting brackets with decorative styling"}],s="all"===e?r:r.filter(t=>t.category===e);return(0,a.jsxs)("div",{className:o().container,children:[(0,a.jsx)("nav",{className:o().navigation,children:(0,a.jsxs)("div",{className:o().navContainer,children:[(0,a.jsxs)("div",{className:o().logo,children:[(0,a.jsx)("h1",{children:"Cast Stone"}),(0,a.jsx)("span",{children:"Interiors & Decorations"})]}),(0,a.jsxs)("ul",{className:o().navMenu,children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/",children:"Home"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/products",className:o().active,children:"Products"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/gallery",children:"Gallery"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/about",children:"About"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/contact",children:"Contact"})})]})]})}),(0,a.jsx)("section",{className:o().hero,children:(0,a.jsxs)("div",{className:o().heroContent,children:[(0,a.jsx)("h1",{className:o().heroTitle,children:"Our Products"}),(0,a.jsx)("p",{className:o().heroSubtitle,children:"Discover our complete collection of handcrafted cast stone elements, from elegant fireplaces to stunning garden features."})]})}),(0,a.jsxs)("section",{className:o().productsSection,children:[(0,a.jsx)("div",{className:o().categoryFilter,children:[{id:"all",name:"All Products"},{id:"fireplaces",name:"Fireplaces"},{id:"garden",name:"Garden Features"},{id:"architectural",name:"Architectural"},{id:"decorative",name:"Decorative"}].map(r=>(0,a.jsx)("button",{className:`${o().categoryBtn} ${e===r.id?o().active:""}`,onClick:()=>t(r.id),children:r.name},r.id))}),(0,a.jsx)("div",{className:o().productsGrid,children:s.map(e=>(0,a.jsxs)("div",{className:o().productCard,children:[(0,a.jsxs)("div",{className:o().productImageContainer,children:[(0,a.jsx)(i.default,{src:e.image,alt:e.name,width:400,height:300,className:o().productImage}),(0,a.jsxs)("div",{className:o().productOverlay,children:[(0,a.jsx)("button",{className:o().viewBtn,children:"View Details"}),(0,a.jsx)("button",{className:o().cartBtn,children:"Add to Cart"})]})]}),(0,a.jsxs)("div",{className:o().productInfo,children:[(0,a.jsx)("h3",{className:o().productName,children:e.name}),(0,a.jsx)("p",{className:o().productDescription,children:e.description}),(0,a.jsxs)("div",{className:o().productFooter,children:[(0,a.jsx)("span",{className:o().productPrice,children:e.price}),(0,a.jsx)("button",{className:o().inquireBtn,children:"Inquire"})]})]})]},e.id))})]}),(0,a.jsx)("section",{className:o().ctaSection,children:(0,a.jsxs)("div",{className:o().ctaContent,children:[(0,a.jsx)("h2",{children:"Need Custom Design?"}),(0,a.jsx)("p",{children:"Our master craftsmen can create bespoke cast stone pieces tailored to your specific requirements."}),(0,a.jsx)("button",{className:o().ctaBtn,children:"Request Custom Quote"})]})}),(0,a.jsxs)("footer",{className:o().footer,children:[(0,a.jsxs)("div",{className:o().footerContent,children:[(0,a.jsxs)("div",{className:o().footerSection,children:[(0,a.jsx)("h3",{children:"Cast Stone"}),(0,a.jsx)("p",{children:"Creating timeless beauty with handcrafted cast stone elements for over 25 years."})]}),(0,a.jsxs)("div",{className:o().footerSection,children:[(0,a.jsx)("h4",{children:"Quick Links"}),(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/products",children:"Products"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/gallery",children:"Gallery"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/about",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/contact",children:"Contact"})})]})]}),(0,a.jsxs)("div",{className:o().footerSection,children:[(0,a.jsx)("h4",{children:"Contact Info"}),(0,a.jsxs)("p",{children:["123 Artisan Way",(0,a.jsx)("br",{}),"Craftsman City, CC 12345"]}),(0,a.jsx)("p",{children:"Phone: (*************"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsx)("div",{className:o().footerBottom,children:(0,a.jsx)("p",{children:"\xa9 2024 Cast Stone Interiors. All rights reserved."})})]})]})}},5883:()=>{},6949:(e,t,r)=>{Promise.resolve().then(r.bind(r,8547))},7404:e=>{e.exports={container:"page_container__MNY9B",navigation:"page_navigation__2pRTH",navContainer:"page_navContainer__92Oxa",logo:"page_logo__CkXj_",navMenu:"page_navMenu__ICbru",active:"page_active__ueGNe",hero:"page_hero__M_w3l",heroContent:"page_heroContent__8l0X0",heroTitle:"page_heroTitle__u3dCo",heroSubtitle:"page_heroSubtitle__zMuar",productsSection:"page_productsSection__p8BO0",categoryFilter:"page_categoryFilter__YbRKg",categoryBtn:"page_categoryBtn__y6TMw",productsGrid:"page_productsGrid__koizS",productCard:"page_productCard__TcT5K",productImageContainer:"page_productImageContainer__TOOvX",productImage:"page_productImage__Riyzl",productOverlay:"page_productOverlay__0_8Mg",viewBtn:"page_viewBtn__LB3A1",cartBtn:"page_cartBtn__Hp2sn",productInfo:"page_productInfo__3g6zS",productName:"page_productName__S8OlF",productDescription:"page_productDescription__20458",productFooter:"page_productFooter__GIYw7",productPrice:"page_productPrice__GQ1oR",inquireBtn:"page_inquireBtn__ADCWS",ctaSection:"page_ctaSection___VqAL",ctaContent:"page_ctaContent___JlH_",ctaBtn:"page_ctaBtn__q7_TQ",footer:"page_footer__4_tZ4",footerContent:"page_footerContent__J_rzp",footerSection:"page_footerSection__8hhlT",footerBottom:"page_footerBottom__427u6"}},8547:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\products\\page.tsx","default")},8714:(e,t,r)=>{Promise.resolve().then(r.bind(r,5613))},8851:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,145,658,533],()=>r(2384));module.exports=a})();