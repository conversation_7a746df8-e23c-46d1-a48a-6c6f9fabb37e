{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/products/page.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"page-module__bSawnG__active\",\n  \"cartBtn\": \"page-module__bSawnG__cartBtn\",\n  \"categoryBtn\": \"page-module__bSawnG__categoryBtn\",\n  \"categoryFilter\": \"page-module__bSawnG__categoryFilter\",\n  \"container\": \"page-module__bSawnG__container\",\n  \"ctaBtn\": \"page-module__bSawnG__ctaBtn\",\n  \"ctaContent\": \"page-module__bSawnG__ctaContent\",\n  \"ctaSection\": \"page-module__bSawnG__ctaSection\",\n  \"footer\": \"page-module__bSawnG__footer\",\n  \"footerBottom\": \"page-module__bSawnG__footerBottom\",\n  \"footerContent\": \"page-module__bSawnG__footerContent\",\n  \"footerSection\": \"page-module__bSawnG__footerSection\",\n  \"hero\": \"page-module__bSawnG__hero\",\n  \"heroContent\": \"page-module__bSawnG__heroContent\",\n  \"heroSubtitle\": \"page-module__bSawnG__heroSubtitle\",\n  \"heroTitle\": \"page-module__bSawnG__heroTitle\",\n  \"inquireBtn\": \"page-module__bSawnG__inquireBtn\",\n  \"logo\": \"page-module__bSawnG__logo\",\n  \"navContainer\": \"page-module__bSawnG__navContainer\",\n  \"navMenu\": \"page-module__bSawnG__navMenu\",\n  \"navigation\": \"page-module__bSawnG__navigation\",\n  \"productCard\": \"page-module__bSawnG__productCard\",\n  \"productDescription\": \"page-module__bSawnG__productDescription\",\n  \"productFooter\": \"page-module__bSawnG__productFooter\",\n  \"productImage\": \"page-module__bSawnG__productImage\",\n  \"productImageContainer\": \"page-module__bSawnG__productImageContainer\",\n  \"productInfo\": \"page-module__bSawnG__productInfo\",\n  \"productName\": \"page-module__bSawnG__productName\",\n  \"productOverlay\": \"page-module__bSawnG__productOverlay\",\n  \"productPrice\": \"page-module__bSawnG__productPrice\",\n  \"productsGrid\": \"page-module__bSawnG__productsGrid\",\n  \"productsSection\": \"page-module__bSawnG__productsSection\",\n  \"viewBtn\": \"page-module__bSawnG__viewBtn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/products/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Image from \"next/image\";\r\nimport styles from \"./page.module.css\";\r\n\r\nexport default function Products() {\r\n  const [selectedCategory, setSelectedCategory] = useState('all');\r\n\r\n  const categories = [\r\n    { id: 'all', name: 'All Products' },\r\n    { id: 'fireplaces', name: 'Fireplaces' },\r\n    { id: 'garden', name: 'Garden Features' },\r\n    { id: 'architectural', name: 'Architectural' },\r\n    { id: 'decorative', name: 'Decorative' }\r\n  ];\r\n\r\n  const products = [\r\n    {\r\n      id: 1,\r\n      name: \"Classic Fireplace Mantel\",\r\n      category: \"fireplaces\",\r\n      price: \"$2,500\",\r\n      image: \"/images/fireplace-collection.jpg\",\r\n      description: \"Handcrafted traditional mantel with intricate detailing\"\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"Modern Fireplace Surround\",\r\n      category: \"fireplaces\",\r\n      price: \"$3,200\",\r\n      image: \"/images/fireplace-collection.jpg\",\r\n      description: \"Contemporary design with clean lines and elegant finish\"\r\n    },\r\n    {\r\n      id: 3,\r\n      name: \"Garden Fountain\",\r\n      category: \"garden\",\r\n      price: \"$1,800\",\r\n      image: \"/images/garden-collection.jpg\",\r\n      description: \"Three-tier fountain perfect for outdoor spaces\"\r\n    },\r\n    {\r\n      id: 4,\r\n      name: \"Decorative Planters\",\r\n      category: \"garden\",\r\n      price: \"$450\",\r\n      image: \"/images/garden-collection.jpg\",\r\n      description: \"Set of elegant planters for garden landscaping\"\r\n    },\r\n    {\r\n      id: 5,\r\n      name: \"Classical Columns\",\r\n      category: \"architectural\",\r\n      price: \"$1,200\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Corinthian style columns for grand entrances\"\r\n    },\r\n    {\r\n      id: 6,\r\n      name: \"Decorative Balustrade\",\r\n      category: \"architectural\",\r\n      price: \"$800\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Ornate balustrade for staircases and terraces\"\r\n    },\r\n    {\r\n      id: 7,\r\n      name: \"Wall Medallions\",\r\n      category: \"decorative\",\r\n      price: \"$350\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Decorative wall medallions for interior accent\"\r\n    },\r\n    {\r\n      id: 8,\r\n      name: \"Ornamental Corbels\",\r\n      category: \"decorative\",\r\n      price: \"$280\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Supporting brackets with decorative styling\"\r\n    }\r\n  ];\r\n\r\n  const filteredProducts = selectedCategory === 'all' \r\n    ? products \r\n    : products.filter(product => product.category === selectedCategory);\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Navigation */}\r\n      <nav className={styles.navigation}>\r\n        <div className={styles.navContainer}>\r\n          <div className={styles.logo}>\r\n            <h1>Cast Stone</h1>\r\n            <span>Interiors & Decorations</span>\r\n          </div>\r\n          <ul className={styles.navMenu}>\r\n            <li><a href=\"/\">Home</a></li>\r\n            <li><a href=\"/products\" className={styles.active}>Products</a></li>\r\n            <li><a href=\"/gallery\">Gallery</a></li>\r\n            <li><a href=\"/about\">About</a></li>\r\n            <li><a href=\"/contact\">Contact</a></li>\r\n          </ul>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Hero Section */}\r\n      <section className={styles.hero}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>Our Products</h1>\r\n          <p className={styles.heroSubtitle}>\r\n            Discover our complete collection of handcrafted cast stone elements, \r\n            from elegant fireplaces to stunning garden features.\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Product Categories */}\r\n      <section className={styles.productsSection}>\r\n        <div className={styles.categoryFilter}>\r\n          {categories.map(category => (\r\n            <button\r\n              key={category.id}\r\n              className={`${styles.categoryBtn} ${\r\n                selectedCategory === category.id ? styles.active : ''\r\n              }`}\r\n              onClick={() => setSelectedCategory(category.id)}\r\n            >\r\n              {category.name}\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Products Grid */}\r\n        <div className={styles.productsGrid}>\r\n          {filteredProducts.map(product => (\r\n            <div key={product.id} className={styles.productCard}>\r\n              <div className={styles.productImageContainer}>\r\n                <Image\r\n                  src={product.image}\r\n                  alt={product.name}\r\n                  width={400}\r\n                  height={300}\r\n                  className={styles.productImage}\r\n                />\r\n                <div className={styles.productOverlay}>\r\n                  <button className={styles.viewBtn}>View Details</button>\r\n                  <button className={styles.cartBtn}>Add to Cart</button>\r\n                </div>\r\n              </div>\r\n              <div className={styles.productInfo}>\r\n                <h3 className={styles.productName}>{product.name}</h3>\r\n                <p className={styles.productDescription}>{product.description}</p>\r\n                <div className={styles.productFooter}>\r\n                  <span className={styles.productPrice}>{product.price}</span>\r\n                  <button className={styles.inquireBtn}>Inquire</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className={styles.ctaSection}>\r\n        <div className={styles.ctaContent}>\r\n          <h2>Need Custom Design?</h2>\r\n          <p>Our master craftsmen can create bespoke cast stone pieces tailored to your specific requirements.</p>\r\n          <button className={styles.ctaBtn}>Request Custom Quote</button>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className={styles.footer}>\r\n        <div className={styles.footerContent}>\r\n          <div className={styles.footerSection}>\r\n            <h3>Cast Stone</h3>\r\n            <p>Creating timeless beauty with handcrafted cast stone elements for over 25 years.</p>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Quick Links</h4>\r\n            <ul>\r\n              <li><a href=\"/products\">Products</a></li>\r\n              <li><a href=\"/gallery\">Gallery</a></li>\r\n              <li><a href=\"/about\">About Us</a></li>\r\n              <li><a href=\"/contact\">Contact</a></li>\r\n            </ul>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Contact Info</h4>\r\n            <p>123 Artisan Way<br />Craftsman City, CC 12345</p>\r\n            <p>Phone: (*************</p>\r\n            <p>Email: <EMAIL></p>\r\n          </div>\r\n        </div>\r\n        <div className={styles.footerBottom}>\r\n          <p>&copy; 2024 Cast Stone Interiors. All rights reserved.</p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;QAAe;QAClC;YAAE,IAAI;YAAc,MAAM;QAAa;QACvC;YAAE,IAAI;YAAU,MAAM;QAAkB;QACxC;YAAE,IAAI;YAAiB,MAAM;QAAgB;QAC7C;YAAE,IAAI;YAAc,MAAM;QAAa;KACxC;IAED,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,qBAAqB,QAC1C,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,qBACE,6LAAC;QAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,6LAAC;gBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,YAAY;;sCACjC,6LAAC;4BAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;4BAAG,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;;8CAC3B,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAI;;;;;;;;;;;8CAChB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;wCAAY,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;kDAAE;;;;;;;;;;;8CAClD,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;8CACvB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;8CACrB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAQ,WAAW,6IAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,6LAAC;oBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,WAAW;;sCAChC,6LAAC;4BAAG,WAAW,6IAAA,CAAA,UAAM,CAAC,SAAS;sCAAE;;;;;;sCACjC,6LAAC;4BAAE,WAAW,6IAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;;;;;;;;;;;;0BAQvC,6LAAC;gBAAQ,WAAW,6IAAA,CAAA,UAAM,CAAC,eAAe;;kCACxC,6LAAC;wBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,cAAc;kCAClC,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;gCAEC,WAAW,GAAG,6IAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAChC,qBAAqB,SAAS,EAAE,GAAG,6IAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IACnD;gCACF,SAAS,IAAM,oBAAoB,SAAS,EAAE;0CAE7C,SAAS,IAAI;+BANT,SAAS,EAAE;;;;;;;;;;kCAYtB,6LAAC;wBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,YAAY;kCAChC,iBAAiB,GAAG,CAAC,CAAA,wBACpB,6LAAC;gCAAqB,WAAW,6IAAA,CAAA,UAAM,CAAC,WAAW;;kDACjD,6LAAC;wCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,qBAAqB;;0DAC1C,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,IAAI;gDACjB,OAAO;gDACP,QAAQ;gDACR,WAAW,6IAAA,CAAA,UAAM,CAAC,YAAY;;;;;;0DAEhC,6LAAC;gDAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,cAAc;;kEACnC,6LAAC;wDAAO,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;kEAAE;;;;;;kEACnC,6LAAC;wDAAO,WAAW,6IAAA,CAAA,UAAM,CAAC,OAAO;kEAAE;;;;;;;;;;;;;;;;;;kDAGvC,6LAAC;wCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,6LAAC;gDAAG,WAAW,6IAAA,CAAA,UAAM,CAAC,WAAW;0DAAG,QAAQ,IAAI;;;;;;0DAChD,6LAAC;gDAAE,WAAW,6IAAA,CAAA,UAAM,CAAC,kBAAkB;0DAAG,QAAQ,WAAW;;;;;;0DAC7D,6LAAC;gDAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;;kEAClC,6LAAC;wDAAK,WAAW,6IAAA,CAAA,UAAM,CAAC,YAAY;kEAAG,QAAQ,KAAK;;;;;;kEACpD,6LAAC;wDAAO,WAAW,6IAAA,CAAA,UAAM,CAAC,UAAU;kEAAE;;;;;;;;;;;;;;;;;;;+BAnBlC,QAAQ,EAAE;;;;;;;;;;;;;;;;0BA4B1B,6LAAC;gBAAQ,WAAW,6IAAA,CAAA,UAAM,CAAC,UAAU;0BACnC,cAAA,6LAAC;oBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,UAAU;;sCAC/B,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAO,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;sCAAE;;;;;;;;;;;;;;;;;0BAKtC,6LAAC;gBAAO,WAAW,6IAAA,CAAA,UAAM,CAAC,MAAM;;kCAC9B,6LAAC;wBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;gCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAE;;;;;;;;;;;;0CAEL,6LAAC;gCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;;0DACC,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAY;;;;;;;;;;;0DACxB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;0DACvB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAS;;;;;;;;;;;0DACrB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;0CAG3B,6LAAC;gCAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;;4CAAE;0DAAe,6LAAC;;;;;4CAAK;;;;;;;kDACxB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAGP,6LAAC;wBAAI,WAAW,6IAAA,CAAA,UAAM,CAAC,YAAY;kCACjC,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb;GApMwB;KAAA", "debugId": null}}]}