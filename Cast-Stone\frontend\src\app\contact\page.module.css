/* Contact Page Styles */

.container {
  min-height: 100vh;
  background: var(--background-light);
  color: var(--text-dark);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Navigation */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.navContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  letter-spacing: -0.02em;
}

.logo span {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.navMenu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.navMenu a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.navMenu a:hover,
.navMenu a.active {
  color: var(--primary-color);
}

.navMenu a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.navMenu a:hover::after,
.navMenu a.active::after {
  width: 100%;
}

/* Hero Section */
.hero {
  padding: 8rem 2rem 4rem;
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
}

.heroSubtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  line-height: 1.7;
}

/* Contact Section */
.contactSection {
  padding: 6rem 2rem;
}

.contactContainer {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

@media (max-width: 968px) {
  .contactContainer {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

/* Form Container */
.formContainer {
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow);
}

.formContainer h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.formContainer p {
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 640px) {
  .formRow {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input,
.textarea,
.select {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.input:focus,
.textarea:focus,
.select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 120px;
}

.select {
  cursor: pointer;
}

.submitButton {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submitButton:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Contact Info */
.contactInfo {
  background: var(--background-light);
  padding: 3rem;
  border-radius: 15px;
}

.contactInfo h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.contactInfo > p {
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.infoGrid {
  display: grid;
  gap: 2rem;
}

.infoItem {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  padding: 1.5rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.infoItem:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.infoIcon {
  font-size: 1.5rem;
  min-width: 40px;
  text-align: center;
}

.infoTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.infoText {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 0.25rem;
}

.infoSubtext {
  color: var(--text-light);
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Footer */
.footer {
  background: var(--text-dark);
  color: white;
  padding: 3rem 2rem 1rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footerSection h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footerSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerSection ul li {
  margin-bottom: 0.5rem;
}

.footerSection ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footerSection ul li a:hover {
  color: var(--secondary-color);
}

.footerBottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .navMenu {
    display: none;
  }

  .formContainer,
  .contactInfo {
    padding: 2rem;
  }

  .contactContainer {
    padding: 0 1rem;
  }

  .formContainer h2,
  .contactInfo h2 {
    font-size: 1.5rem;
  }
}
