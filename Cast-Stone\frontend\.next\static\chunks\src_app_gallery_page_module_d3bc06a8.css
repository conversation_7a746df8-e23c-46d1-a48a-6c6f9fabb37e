/* [project]/src/app/gallery/page.module.css [app-client] (css) */
.page-module__8IDnjq__container {
  background: var(--background-light);
  min-height: 100vh;
  color: var(--text-dark);
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
}

.page-module__8IDnjq__navigation {
  backdrop-filter: blur(10px);
  z-index: 1000;
  background: #fffffff2;
  border-bottom: 1px solid #8b45131a;
  padding: 1rem 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.page-module__8IDnjq__navContainer {
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
}

.page-module__8IDnjq__logo h1 {
  color: var(--primary-color);
  letter-spacing: -.02em;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-module__8IDnjq__logo span {
  color: var(--text-light);
  font-size: .9rem;
  font-weight: 400;
}

.page-module__8IDnjq__navMenu {
  gap: 2rem;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.page-module__8IDnjq__navMenu a {
  color: var(--text-dark);
  font-weight: 500;
  text-decoration: none;
  transition: color .3s;
  position: relative;
}

.page-module__8IDnjq__navMenu a:hover, .page-module__8IDnjq__navMenu a.page-module__8IDnjq__active {
  color: var(--primary-color);
}

.page-module__8IDnjq__navMenu a:after {
  content: "";
  background: var(--primary-color);
  width: 0;
  height: 2px;
  transition: width .3s;
  position: absolute;
  bottom: -5px;
  left: 0;
}

.page-module__8IDnjq__navMenu a:hover:after, .page-module__8IDnjq__navMenu a.page-module__8IDnjq__active:after {
  width: 100%;
}

.page-module__8IDnjq__hero {
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: #fff;
  padding: 8rem 2rem 4rem;
}

.page-module__8IDnjq__heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.page-module__8IDnjq__heroTitle {
  margin-bottom: 1.5rem;
  font-size: 3.5rem;
  font-weight: 800;
}

.page-module__8IDnjq__heroSubtitle {
  opacity: .9;
  font-size: 1.2rem;
  line-height: 1.7;
}

.page-module__8IDnjq__gallerySection {
  max-width: 1400px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

.page-module__8IDnjq__categoryFilter {
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  display: flex;
}

.page-module__8IDnjq__categoryBtn {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  cursor: pointer;
  background: none;
  border-radius: 50px;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__8IDnjq__categoryBtn:hover, .page-module__8IDnjq__categoryBtn.page-module__8IDnjq__active {
  background: var(--primary-color);
  color: #fff;
  transform: translateY(-2px);
}

.page-module__8IDnjq__galleryGrid {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  display: grid;
}

.page-module__8IDnjq__galleryItem {
  cursor: pointer;
  box-shadow: var(--shadow);
  background: #fff;
  border-radius: 15px;
  transition: all .3s;
  overflow: hidden;
}

.page-module__8IDnjq__galleryItem:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-10px);
}

.page-module__8IDnjq__imageContainer {
  height: 300px;
  position: relative;
  overflow: hidden;
}

.page-module__8IDnjq__galleryImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.page-module__8IDnjq__galleryItem:hover .page-module__8IDnjq__galleryImage {
  transform: scale(1.1);
}

.page-module__8IDnjq__imageOverlay {
  color: #fff;
  background: linear-gradient(#0000, #000c);
  padding: 2rem;
  transition: transform .3s;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  transform: translateY(100%);
}

.page-module__8IDnjq__galleryItem:hover .page-module__8IDnjq__imageOverlay {
  transform: translateY(0);
}

.page-module__8IDnjq__imageTitle {
  margin-bottom: .5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.page-module__8IDnjq__imageDescription {
  opacity: .9;
  margin-bottom: 1rem;
  font-size: .9rem;
}

.page-module__8IDnjq__viewBtn {
  background: var(--primary-color);
  color: #fff;
  cursor: pointer;
  border: none;
  border-radius: 20px;
  padding: .5rem 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__8IDnjq__viewBtn:hover {
  background: var(--accent-color);
  transform: translateY(-2px);
}

.page-module__8IDnjq__modal {
  z-index: 2000;
  background: #000000e6;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  display: flex;
  position: fixed;
  inset: 0;
}

.page-module__8IDnjq__modalContent {
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: var(--shadow-hover);
  background: #fff;
  border-radius: 15px;
  position: relative;
  overflow: hidden;
}

.page-module__8IDnjq__closeBtn {
  color: #fff;
  cursor: pointer;
  z-index: 10;
  background: #000000b3;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.5rem;
  transition: background .3s;
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.page-module__8IDnjq__closeBtn:hover {
  background: #000000e6;
}

.page-module__8IDnjq__modalImage {
  object-fit: contain;
  width: 100%;
  height: auto;
  max-height: 70vh;
}

.page-module__8IDnjq__modalInfo {
  padding: 2rem;
}

.page-module__8IDnjq__modalInfo h2 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-module__8IDnjq__modalInfo p {
  color: var(--text-light);
  line-height: 1.6;
}

.page-module__8IDnjq__ctaSection {
  background: var(--primary-color);
  color: #fff;
  text-align: center;
  padding: 4rem 2rem;
}

.page-module__8IDnjq__ctaContent h2 {
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module__8IDnjq__ctaContent p {
  opacity: .9;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.page-module__8IDnjq__ctaBtn {
  color: var(--primary-color);
  cursor: pointer;
  background: #fff;
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__8IDnjq__ctaBtn:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.page-module__8IDnjq__footer {
  background: var(--text-dark);
  color: #fff;
  padding: 3rem 2rem 1rem;
}

.page-module__8IDnjq__footerContent {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto 2rem;
  display: grid;
}

.page-module__8IDnjq__footerSection h3 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.page-module__8IDnjq__footerSection h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.page-module__8IDnjq__footerSection p {
  color: #fffc;
  line-height: 1.6;
}

.page-module__8IDnjq__footerSection ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__8IDnjq__footerSection ul li {
  margin-bottom: .5rem;
}

.page-module__8IDnjq__footerSection ul li a {
  color: #fffc;
  text-decoration: none;
  transition: color .3s;
}

.page-module__8IDnjq__footerSection ul li a:hover {
  color: var(--secondary-color);
}

.page-module__8IDnjq__footerBottom {
  text-align: center;
  color: #fff9;
  border-top: 1px solid #ffffff1a;
  padding-top: 1rem;
}

@media (width <= 768px) {
  .page-module__8IDnjq__heroTitle {
    font-size: 2.5rem;
  }

  .page-module__8IDnjq__navMenu {
    display: none;
  }

  .page-module__8IDnjq__galleryGrid {
    grid-template-columns: 1fr;
  }

  .page-module__8IDnjq__categoryFilter {
    gap: .5rem;
  }

  .page-module__8IDnjq__categoryBtn {
    padding: .5rem 1rem;
    font-size: .9rem;
  }

  .page-module__8IDnjq__modal, .page-module__8IDnjq__modalInfo {
    padding: 1rem;
  }
}


/*# sourceMappingURL=src_app_gallery_page_module_d3bc06a8.css.map*/