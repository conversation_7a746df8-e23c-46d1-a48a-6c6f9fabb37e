/* [project]/src/app/products/page.module.css [app-client] (css) */
:root {
  --primary-color: #8b4513;
  --secondary-color: tan;
  --accent-color: peru;
  --text-dark: #2c1810;
  --text-light: #6b5b4f;
  --background-light: #faf7f2;
  --white: #fff;
  --shadow: 0 10px 30px #0000001a;
  --shadow-hover: 0 20px 40px #00000026;
}

.page-module__bSawnG__container {
  background: var(--background-light);
  min-height: 100vh;
  color: var(--text-dark);
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
}

.page-module__bSawnG__navigation {
  backdrop-filter: blur(10px);
  z-index: 1000;
  background: #fffffff2;
  border-bottom: 1px solid #8b45131a;
  padding: 1rem 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.page-module__bSawnG__navContainer {
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
}

.page-module__bSawnG__logo h1 {
  color: var(--primary-color);
  letter-spacing: -.02em;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-module__bSawnG__logo span {
  color: var(--text-light);
  font-size: .9rem;
  font-weight: 400;
}

.page-module__bSawnG__navMenu {
  gap: 2rem;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.page-module__bSawnG__navMenu a {
  color: var(--text-dark);
  font-weight: 500;
  text-decoration: none;
  transition: color .3s;
  position: relative;
}

.page-module__bSawnG__navMenu a:hover, .page-module__bSawnG__navMenu a.page-module__bSawnG__active {
  color: var(--primary-color);
}

.page-module__bSawnG__navMenu a:after {
  content: "";
  background: var(--primary-color);
  width: 0;
  height: 2px;
  transition: width .3s;
  position: absolute;
  bottom: -5px;
  left: 0;
}

.page-module__bSawnG__navMenu a:hover:after, .page-module__bSawnG__navMenu a.page-module__bSawnG__active:after {
  width: 100%;
}

.page-module__bSawnG__hero {
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: #fff;
  padding: 8rem 2rem 4rem;
}

.page-module__bSawnG__heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.page-module__bSawnG__heroTitle {
  margin-bottom: 1.5rem;
  font-size: 3.5rem;
  font-weight: 800;
}

.page-module__bSawnG__heroSubtitle {
  opacity: .9;
  font-size: 1.2rem;
  line-height: 1.7;
}

.page-module__bSawnG__productsSection {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

.page-module__bSawnG__categoryFilter {
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  display: flex;
}

.page-module__bSawnG__categoryBtn {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  cursor: pointer;
  background: none;
  border-radius: 50px;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__bSawnG__categoryBtn:hover, .page-module__bSawnG__categoryBtn.page-module__bSawnG__active {
  background: var(--primary-color);
  color: #fff;
  transform: translateY(-2px);
}

.page-module__bSawnG__productsGrid {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  display: grid;
}

.page-module__bSawnG__productCard {
  box-shadow: var(--shadow);
  background: #fff;
  border-radius: 15px;
  transition: all .3s;
  overflow: hidden;
}

.page-module__bSawnG__productCard:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-10px);
}

.page-module__bSawnG__productImageContainer {
  position: relative;
  overflow: hidden;
}

.page-module__bSawnG__productImage {
  object-fit: cover;
  width: 100%;
  height: 250px;
  transition: transform .3s;
}

.page-module__bSawnG__productCard:hover .page-module__bSawnG__productImage {
  transform: scale(1.05);
}

.page-module__bSawnG__productOverlay {
  opacity: 0;
  background: #000000b3;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  transition: opacity .3s;
  display: flex;
  position: absolute;
  inset: 0;
}

.page-module__bSawnG__productCard:hover .page-module__bSawnG__productOverlay {
  opacity: 1;
}

.page-module__bSawnG__viewBtn, .page-module__bSawnG__cartBtn {
  cursor: pointer;
  border: none;
  border-radius: 25px;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__bSawnG__viewBtn {
  color: var(--primary-color);
  background: #fff;
}

.page-module__bSawnG__cartBtn {
  background: var(--primary-color);
  color: #fff;
}

.page-module__bSawnG__viewBtn:hover, .page-module__bSawnG__cartBtn:hover {
  transform: translateY(-2px);
}

.page-module__bSawnG__productInfo {
  padding: 1.5rem;
}

.page-module__bSawnG__productName {
  color: var(--text-dark);
  margin-bottom: .5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.page-module__bSawnG__productDescription {
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.page-module__bSawnG__productFooter {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.page-module__bSawnG__productPrice {
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 700;
}

.page-module__bSawnG__inquireBtn {
  background: var(--accent-color);
  color: #fff;
  cursor: pointer;
  border: none;
  border-radius: 20px;
  padding: .5rem 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__bSawnG__inquireBtn:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}

.page-module__bSawnG__ctaSection {
  background: var(--primary-color);
  color: #fff;
  text-align: center;
  padding: 4rem 2rem;
}

.page-module__bSawnG__ctaContent h2 {
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module__bSawnG__ctaContent p {
  opacity: .9;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.page-module__bSawnG__ctaBtn {
  color: var(--primary-color);
  cursor: pointer;
  background: #fff;
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__bSawnG__ctaBtn:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.page-module__bSawnG__footer {
  background: var(--text-dark);
  color: #fff;
  padding: 3rem 2rem 1rem;
}

.page-module__bSawnG__footerContent {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto 2rem;
  display: grid;
}

.page-module__bSawnG__footerSection h3 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.page-module__bSawnG__footerSection h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.page-module__bSawnG__footerSection p {
  color: #fffc;
  line-height: 1.6;
}

.page-module__bSawnG__footerSection ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__bSawnG__footerSection ul li {
  margin-bottom: .5rem;
}

.page-module__bSawnG__footerSection ul li a {
  color: #fffc;
  text-decoration: none;
  transition: color .3s;
}

.page-module__bSawnG__footerSection ul li a:hover {
  color: var(--secondary-color);
}

.page-module__bSawnG__footerBottom {
  text-align: center;
  color: #fff9;
  border-top: 1px solid #ffffff1a;
  padding-top: 1rem;
}

@media (width <= 768px) {
  .page-module__bSawnG__heroTitle {
    font-size: 2.5rem;
  }

  .page-module__bSawnG__navMenu {
    display: none;
  }

  .page-module__bSawnG__categoryFilter {
    gap: .5rem;
  }

  .page-module__bSawnG__categoryBtn {
    padding: .5rem 1rem;
    font-size: .9rem;
  }

  .page-module__bSawnG__productsGrid {
    grid-template-columns: 1fr;
  }
}


/*# sourceMappingURL=src_app_products_page_module_4e350656.css.map*/