(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return l},getImageProps:function(){return n}});let a=t(8229),r=t(8883),i=t(3063),o=a._(t(1193));function n(e){let{props:s}=(0,r.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let l=i.Image},3792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(5155),r=t(2115),i=t(6766),o=t(8334),n=t.n(o);function l(){let[e,s]=(0,r.useState)(0),t=[{id:1,title:"Luxury Cast Stone Fireplaces",description:"Transform your living space with our handcrafted cast stone fireplaces",videoUrl:"/videos/fireplace-demo.mp4",thumbnail:"/images/fireplace-thumb.jpg"},{id:2,title:"Elegant Garden Features",description:"Create stunning outdoor spaces with our cast stone garden elements",videoUrl:"/videos/garden-demo.mp4",thumbnail:"/images/garden-thumb.jpg"},{id:3,title:"Architectural Details",description:"Add sophistication with our custom cast stone architectural elements",videoUrl:"/videos/architecture-demo.mp4",thumbnail:"/images/architecture-thumb.jpg"}];return(0,r.useEffect)(()=>{let e=setInterval(()=>{s(e=>(e+1)%t.length)},5e3);return()=>clearInterval(e)},[t.length]),(0,a.jsxs)("div",{className:n().container,children:[(0,a.jsx)("nav",{className:n().navigation,children:(0,a.jsxs)("div",{className:n().navContainer,children:[(0,a.jsxs)("div",{className:n().logo,children:[(0,a.jsx)("h1",{children:"Cast Stone"}),(0,a.jsx)("span",{children:"Interiors & Decorations"})]}),(0,a.jsxs)("ul",{className:n().navMenu,children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/",children:"Home"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/products",children:"Products"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/gallery",children:"Gallery"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/about",children:"About"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/contact",children:"Contact"})})]})]})}),(0,a.jsxs)("section",{className:n().hero,children:[(0,a.jsxs)("div",{className:n().heroContent,children:[(0,a.jsxs)("h1",{className:n().heroTitle,children:["Timeless Elegance in",(0,a.jsx)("span",{className:n().highlight,children:" Cast Stone"})]}),(0,a.jsx)("p",{className:n().heroSubtitle,children:"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art."}),(0,a.jsxs)("div",{className:n().heroActions,children:[(0,a.jsx)("button",{className:n().primaryBtn,children:"Explore Collection"}),(0,a.jsx)("button",{className:n().secondaryBtn,children:"Watch Our Story"})]})]}),(0,a.jsx)("div",{className:n().heroImage,children:(0,a.jsx)(i.default,{src:"/images/hero-cast-stone.jpg",alt:"Luxury Cast Stone Interior",width:800,height:600,priority:!0,className:n().heroImg})})]}),(0,a.jsxs)("section",{className:n().videoSection,children:[(0,a.jsxs)("div",{className:n().sectionHeader,children:[(0,a.jsx)("h2",{children:"Experience Our Craftsmanship"}),(0,a.jsx)("p",{children:"Watch how we transform spaces with our premium cast stone creations"})]}),(0,a.jsx)("div",{className:n().videoCarousel,children:t.map((s,t)=>(0,a.jsx)("div",{className:"".concat(n().videoSlide," ").concat(t===e?n().active:""," ").concat(t%2==0?n().slideFromLeft:n().slideFromRight),children:(0,a.jsxs)("div",{className:n().videoContainer,children:[(0,a.jsxs)("div",{className:n().videoPlaceholder,children:[(0,a.jsx)("div",{className:n().playButton,children:(0,a.jsxs)("svg",{width:"60",height:"60",viewBox:"0 0 60 60",children:[(0,a.jsx)("circle",{cx:"30",cy:"30",r:"30",fill:"rgba(255,255,255,0.9)"}),(0,a.jsx)("polygon",{points:"24,18 24,42 42,30",fill:"#333"})]})}),(0,a.jsx)(i.default,{src:s.thumbnail||"/images/placeholder-video.jpg",alt:s.title,width:600,height:400,className:n().videoThumbnail})]}),(0,a.jsxs)("div",{className:n().videoInfo,children:[(0,a.jsx)("h3",{children:s.title}),(0,a.jsx)("p",{children:s.description})]})]})},s.id))}),(0,a.jsx)("div",{className:n().videoControls,children:t.map((t,r)=>(0,a.jsx)("button",{className:"".concat(n().videoDot," ").concat(r===e?n().activeDot:""),onClick:()=>s(r)},r))})]}),(0,a.jsxs)("section",{className:n().featuredProducts,children:[(0,a.jsxs)("div",{className:n().sectionHeader,children:[(0,a.jsx)("h2",{children:"Featured Collections"}),(0,a.jsx)("p",{children:"Discover our most popular cast stone pieces"})]}),(0,a.jsxs)("div",{className:n().productGrid,children:[(0,a.jsxs)("div",{className:n().productCard,children:[(0,a.jsx)(i.default,{src:"/images/fireplace-collection.jpg",alt:"Fireplace Collection",width:400,height:300,className:n().productImage}),(0,a.jsxs)("div",{className:n().productInfo,children:[(0,a.jsx)("h3",{children:"Fireplace Collection"}),(0,a.jsx)("p",{children:"Handcrafted mantels and surrounds"}),(0,a.jsx)("span",{className:n().productPrice,children:"From $2,500"})]})]}),(0,a.jsxs)("div",{className:n().productCard,children:[(0,a.jsx)(i.default,{src:"/images/garden-collection.jpg",alt:"Garden Collection",width:400,height:300,className:n().productImage}),(0,a.jsxs)("div",{className:n().productInfo,children:[(0,a.jsx)("h3",{children:"Garden Features"}),(0,a.jsx)("p",{children:"Fountains, planters, and sculptures"}),(0,a.jsx)("span",{className:n().productPrice,children:"From $800"})]})]}),(0,a.jsxs)("div",{className:n().productCard,children:[(0,a.jsx)(i.default,{src:"/images/architectural-collection.jpg",alt:"Architectural Collection",width:400,height:300,className:n().productImage}),(0,a.jsxs)("div",{className:n().productInfo,children:[(0,a.jsx)("h3",{children:"Architectural Elements"}),(0,a.jsx)("p",{children:"Columns, balustrades, and moldings"}),(0,a.jsx)("span",{className:n().productPrice,children:"From $1,200"})]})]})]})]}),(0,a.jsx)("section",{className:n().newsletter,children:(0,a.jsxs)("div",{className:n().newsletterContent,children:[(0,a.jsx)("h2",{children:"Stay Inspired"}),(0,a.jsx)("p",{children:"Get the latest design trends and exclusive offers delivered to your inbox"}),(0,a.jsxs)("div",{className:n().newsletterForm,children:[(0,a.jsx)("input",{type:"email",placeholder:"Enter your email address",className:n().emailInput}),(0,a.jsx)("button",{className:n().subscribeBtn,children:"Subscribe"})]})]})}),(0,a.jsxs)("footer",{className:n().footer,children:[(0,a.jsxs)("div",{className:n().footerContent,children:[(0,a.jsxs)("div",{className:n().footerSection,children:[(0,a.jsx)("h3",{children:"Cast Stone"}),(0,a.jsx)("p",{children:"Creating timeless beauty with handcrafted cast stone elements for over 25 years."})]}),(0,a.jsxs)("div",{className:n().footerSection,children:[(0,a.jsx)("h4",{children:"Quick Links"}),(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/products",children:"Products"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/gallery",children:"Gallery"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/about",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/contact",children:"Contact"})})]})]}),(0,a.jsxs)("div",{className:n().footerSection,children:[(0,a.jsx)("h4",{children:"Contact Info"}),(0,a.jsxs)("p",{children:["123 Artisan Way",(0,a.jsx)("br",{}),"Craftsman City, CC 12345"]}),(0,a.jsx)("p",{children:"Phone: (*************"}),(0,a.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,a.jsx)("div",{className:n().footerBottom,children:(0,a.jsx)("p",{children:"\xa9 2024 Cast Stone Interiors. All rights reserved."})})]})]})}},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>r.a});var a=t(1469),r=t.n(a)},8334:e=>{e.exports={container:"page_container__aoG4z",navigation:"page_navigation__sIIfG",navContainer:"page_navContainer__DkD_r",logo:"page_logo__7fc9l",navMenu:"page_navMenu__1H8jk",hero:"page_hero__SKW6o",heroContent:"page_heroContent__2lPR8",heroTitle:"page_heroTitle__Gfler",highlight:"page_highlight__KaGfy",heroSubtitle:"page_heroSubtitle__RTAw0",heroActions:"page_heroActions__8_tzb",primaryBtn:"page_primaryBtn__smNNv",secondaryBtn:"page_secondaryBtn__Re3F8",heroImage:"page_heroImage__Q6NCQ",heroImg:"page_heroImg__Q4Nxm",sectionHeader:"page_sectionHeader__a4Fw5",videoSection:"page_videoSection__OHo_Q",videoCarousel:"page_videoCarousel__YWCit",videoSlide:"page_videoSlide__SQVfa",active:"page_active__q3_T3",slideFromLeft:"page_slideFromLeft__6d8kZ",slideFromRight:"page_slideFromRight__mxQie",videoContainer:"page_videoContainer__dDrrq",videoPlaceholder:"page_videoPlaceholder__TDeI7",videoThumbnail:"page_videoThumbnail__rxPZz",playButton:"page_playButton__egZrD",videoInfo:"page_videoInfo__bv2xv",videoControls:"page_videoControls__rsGt_",videoDot:"page_videoDot__v_JTG",activeDot:"page_activeDot__uXfHl",featuredProducts:"page_featuredProducts__0nTqm",productGrid:"page_productGrid__GJuvN",productCard:"page_productCard__LtAQV",productImage:"page_productImage__ApZwb",productInfo:"page_productInfo__6QZeh",productPrice:"page_productPrice__9UDVu",newsletter:"page_newsletter__86quH",newsletterContent:"page_newsletterContent__On6Jf",newsletterForm:"page_newsletterForm__afwak",emailInput:"page_emailInput__6WJmn",subscribeBtn:"page_subscribeBtn__sZTMw",footer:"page_footer__sHKi3",footerContent:"page_footerContent__sUmFz",footerSection:"page_footerSection__t2TqJ",footerBottom:"page_footerBottom__cWZ_v"}},8416:(e,s,t)=>{Promise.resolve().then(t.bind(t,3792))}},e=>{var s=s=>e(e.s=s);e.O(0,[397,63,441,684,358],()=>s(8416)),_N_E=e.O()}]);