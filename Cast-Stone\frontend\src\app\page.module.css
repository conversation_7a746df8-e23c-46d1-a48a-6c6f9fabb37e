/* Cast Stone Homepage Styles */

.container {
  min-height: 100vh;
  background: var(--background-light);
  color: var(--text-dark);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
}

/* Top Navigation Bar */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.navContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  letter-spacing: -0.02em;
}

.logo span {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.navMenu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
  align-items: center;
}

.navMenu > li {
  position: relative;
}

.navMenu a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
  padding: 0.5rem 0;
}

.navMenu a:hover {
  color: var(--primary-color);
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdownToggle::after {
  content: '▼';
  font-size: 0.7rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.dropdown:hover .dropdownToggle::after {
  transform: rotate(180deg);
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 200px;
  box-shadow: var(--shadow);
  border-radius: 8px;
  padding: 0.5rem 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
}

.dropdown:hover .dropdownMenu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdownMenu li {
  margin: 0;
}

.dropdownMenu a {
  display: block;
  padding: 0.75rem 1.5rem;
  color: var(--text-dark);
  text-decoration: none;
  transition: background-color 0.3s ease;
  font-weight: 400;
}

.dropdownMenu a:hover {
  background-color: var(--background-light);
  color: var(--primary-color);
}

/* Hero Section with Video Background */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.videoBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.highlight {
  color: var(--secondary-color);
}

.heroSubtitle {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.heroActions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryBtn {
  padding: 1rem 2.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primaryBtn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-hover);
}

.secondaryBtn {
  padding: 1rem 2.5rem;
  background: transparent;
  color: white;
  border: 2px solid white;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryBtn:hover {
  background: white;
  color: var(--primary-color);
  transform: translateY(-3px);
}

/* Section Headers */
.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.sectionHeader h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.sectionHeader p {
  font-size: 1.1rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto;
}

/* Categories Carousel */
.categoriesSection {
  padding: 6rem 2rem;
  background: white;
}

.categoriesCarousel {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 15px;
}

.categorySlide {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.categorySlide.active {
  display: block;
  opacity: 1;
}

.categoryCard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  padding: 3rem;
  background: var(--background-light);
  border-radius: 15px;
}

.categoryImage {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.categoryImg {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.categoryCard:hover .categoryImg {
  transform: scale(1.05);
}

.categoryInfo {
  padding: 2rem;
}

.categoryInfo h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.categoryInfo p {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.categoryBtn {
  padding: 0.75rem 2rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.categoryBtn:hover {
  background: var(--accent-color);
  transform: translateY(-2px);
}

.categoryControls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.categoryDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.categoryDot.activeDot {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Online Catalog Section */
.catalogSection {
  padding: 6rem 2rem;
  background: var(--primary-color);
  color: white;
}

.catalogContainer {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.catalogContent {
  padding: 2rem;
}

.catalogText h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: white;
}

.catalogText p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.catalogBtn {
  padding: 1rem 2.5rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.catalogBtn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
}

.catalogImage {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
}

.catalogImg {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.catalogContainer:hover .catalogImg {
  transform: scale(1.05);
}

/* Weekly Featured Products */
.featuredProducts {
  padding: 6rem 2rem;
  background: white;
}

.productCarousel {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
}

.productSlide {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.productSlide.active {
  display: block;
  opacity: 1;
}

.productCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  max-width: 500px;
  margin: 0 auto;
}

.productCard:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
}

.productImageContainer {
  position: relative;
  overflow: hidden;
}

.productImage {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productCard:hover .productImage {
  transform: scale(1.05);
}

.productOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.productCard:hover .productOverlay {
  opacity: 1;
}

.quickViewBtn {
  padding: 0.75rem 1.5rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quickViewBtn:hover {
  transform: translateY(-2px);
}

.productInfo {
  padding: 2rem;
}

.productInfo h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.productInfo p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.productFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.productPrice {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
}

.inquireBtn {
  padding: 0.5rem 1.5rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.inquireBtn:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}

.productControls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.productDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.productDot.activeDot {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Testimonials Section */
.testimonialsSection {
  padding: 6rem 2rem;
  background: var(--background-light);
}

.testimonialsCarousel {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

.testimonialSlide {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.testimonialSlide.active {
  display: block;
  opacity: 1;
}

.testimonialCard {
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow);
  text-align: center;
}

.stars {
  margin-bottom: 1.5rem;
}

.star {
  color: #ffd700;
  font-size: 1.5rem;
  margin: 0 0.1rem;
}

.testimonialText {
  font-size: 1.2rem;
  font-style: italic;
  color: var(--text-dark);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.testimonialAuthor h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.testimonialAuthor span {
  color: var(--text-light);
  font-size: 0.9rem;
}

.testimonialControls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.testimonialDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.testimonialDot.activeDot {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Footer */
.footer {
  background: var(--text-dark);
  color: white;
  padding: 4rem 2rem 2rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 2rem;
}

.footerSection h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.footerSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerSection ul li {
  margin-bottom: 0.5rem;
}

.footerSection ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footerSection ul li a:hover {
  color: var(--secondary-color);
}

.footerBottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Navigation */
  .navMenu {
    display: none; /* Will implement mobile menu later */
  }

  /* Hero Section */
  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
  }

  .heroActions {
    flex-direction: column;
    align-items: center;
  }

  /* Categories */
  .categoryCard {
    grid-template-columns: 1fr;
    text-align: center;
  }

  /* Catalog */
  .catalogContainer {
    grid-template-columns: 1fr;
    text-align: center;
  }

  /* Section Headers */
  .sectionHeader h2 {
    font-size: 2rem;
  }

  /* Testimonials */
  .testimonialCard {
    padding: 2rem;
  }

  .testimonialText {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroContent {
    padding: 0 1rem;
  }

  .primaryBtn,
  .secondaryBtn {
    padding: 0.75rem 2rem;
    font-size: 1rem;
  }

  .sectionHeader h2 {
    font-size: 1.8rem;
  }

  .categoryInfo,
  .testimonialCard {
    padding: 1.5rem;
  }
}

.heroContent {
  max-width: 500px;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--text-dark);
}

.highlight {
  color: var(--primary-color);
  position: relative;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.heroSubtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 2.5rem;
  line-height: 1.7;
}

.heroActions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.primaryBtn {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
}

.primaryBtn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.secondaryBtn {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryBtn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.heroImage {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  box-shadow: var(--shadow);
}

.heroImg {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.heroImage:hover .heroImg {
  transform: scale(1.05);
}

/* Section Headers */
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.sectionHeader h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.sectionHeader p {
  font-size: 1.1rem;
  color: var(--text-light);
}

/* Video Carousel Section */
.videoSection {
  padding: 6rem 2rem;
  background: white;
  overflow: hidden;
}

.videoCarousel {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.videoSlide {
  position: absolute;
  width: 100%;
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.videoSlide.active {
  opacity: 1;
  transform: translateX(0);
  pointer-events: all;
}

.slideFromLeft {
  transform: translateX(-100px);
}

.slideFromLeft.active {
  transform: translateX(0);
}

.slideFromRight {
  transform: translateX(100px);
}

.slideFromRight.active {
  transform: translateX(0);
}

.videoContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  max-width: 900px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .videoContainer {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

.videoPlaceholder {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.videoPlaceholder:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.videoThumbnail {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.playButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease;
}

.videoPlaceholder:hover .playButton {
  transform: translate(-50%, -50%) scale(1.1);
}

.videoInfo h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.videoInfo p {
  font-size: 1.1rem;
  color: var(--text-light);
  line-height: 1.6;
}

.videoControls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 3rem;
}

.videoDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.videoDot.activeDot {
  background: var(--primary-color);
  transform: scale(1.2);
}

.videoDot:hover {
  background: var(--primary-color);
}

/* Featured Products */
.featuredProducts {
  padding: 6rem 2rem;
  background: var(--background-light);
}

.productGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.productCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  cursor: pointer;
}

.productCard:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
}

.productImage {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.productInfo {
  padding: 1.5rem;
}

.productInfo h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.productInfo p {
  color: var(--text-light);
  margin-bottom: 1rem;
}

.productPrice {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Newsletter */
.newsletter {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  text-align: center;
}

.newsletterContent h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.newsletterContent p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.newsletterForm {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
  gap: 1rem;
}

@media (max-width: 640px) {
  .newsletterForm {
    flex-direction: column;
  }
}

.emailInput {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  outline: none;
}

.subscribeBtn {
  background: white;
  color: var(--primary-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.subscribeBtn:hover {
  background: var(--background-light);
  transform: translateY(-2px);
}

/* Footer */
.footer {
  background: var(--text-dark);
  color: white;
  padding: 3rem 2rem 1rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footerSection h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footerSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerSection ul li {
  margin-bottom: 0.5rem;
}

.footerSection ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footerSection ul li a:hover {
  color: var(--secondary-color);
}

.footerBottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .sectionHeader h2 {
    font-size: 2rem;
  }

  .navMenu {
    display: none;
  }

  .videoCarousel {
    height: auto;
  }

  .videoSlide {
    position: relative;
    opacity: 1;
    transform: none;
  }

  .videoSlide.active {
    display: block;
  }

  .videoSlide:not(.active) {
    display: none;
  }
}
