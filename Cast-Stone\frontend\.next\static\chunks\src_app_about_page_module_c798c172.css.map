{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/about/page.module.css"], "sourcesContent": ["/* About Page Styles */\r\n\r\n.container {\r\n  min-height: 100vh;\r\n  background: var(--background-light);\r\n  color: var(--text-dark);\r\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\r\n}\r\n\r\n/* Navigation */\r\n.navigation {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 1000;\r\n  padding: 1rem 0;\r\n  border-bottom: 1px solid rgba(139, 69, 19, 0.1);\r\n}\r\n\r\n.navContainer {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo h1 {\r\n  font-size: 1.8rem;\r\n  font-weight: 700;\r\n  color: var(--primary-color);\r\n  margin: 0;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.logo span {\r\n  font-size: 0.9rem;\r\n  color: var(--text-light);\r\n  font-weight: 400;\r\n}\r\n\r\n.navMenu {\r\n  display: flex;\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  gap: 2rem;\r\n}\r\n\r\n.navMenu a {\r\n  text-decoration: none;\r\n  color: var(--text-dark);\r\n  font-weight: 500;\r\n  transition: color 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.navMenu a:hover,\r\n.navMenu a.active {\r\n  color: var(--primary-color);\r\n}\r\n\r\n.navMenu a::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 0;\r\n  width: 0;\r\n  height: 2px;\r\n  background: var(--primary-color);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.navMenu a:hover::after,\r\n.navMenu a.active::after {\r\n  width: 100%;\r\n}\r\n\r\n/* Hero Section */\r\n.hero {\r\n  padding: 8rem 2rem 4rem;\r\n  text-align: center;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\r\n  color: white;\r\n}\r\n\r\n.heroContent {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.heroTitle {\r\n  font-size: 3.5rem;\r\n  font-weight: 800;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.heroSubtitle {\r\n  font-size: 1.2rem;\r\n  opacity: 0.9;\r\n  line-height: 1.7;\r\n}\r\n\r\n/* Story Section */\r\n.storySection {\r\n  padding: 6rem 2rem;\r\n  background: white;\r\n}\r\n\r\n.storyContainer {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  align-items: center;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .storyContainer {\r\n    grid-template-columns: 1fr;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.storyContent h2 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-dark);\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.storyContent p {\r\n  font-size: 1.1rem;\r\n  color: var(--text-light);\r\n  line-height: 1.7;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.storyImage {\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n.storyImg {\r\n  width: 100%;\r\n  height: auto;\r\n  object-fit: cover;\r\n}\r\n\r\n/* Section Headers */\r\n.sectionHeader {\r\n  text-align: center;\r\n  margin-bottom: 4rem;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.sectionHeader h2 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-dark);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.sectionHeader p {\r\n  font-size: 1.1rem;\r\n  color: var(--text-light);\r\n}\r\n\r\n/* Values Section */\r\n.valuesSection {\r\n  padding: 6rem 2rem;\r\n  background: var(--background-light);\r\n}\r\n\r\n.valuesGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.valueCard {\r\n  background: white;\r\n  padding: 2rem;\r\n  border-radius: 15px;\r\n  text-align: center;\r\n  box-shadow: var(--shadow);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.valueCard:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: var(--shadow-hover);\r\n}\r\n\r\n.valueIcon {\r\n  font-size: 3rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.valueCard h3 {\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  color: var(--text-dark);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.valueCard p {\r\n  color: var(--text-light);\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Team Section */\r\n.teamSection {\r\n  padding: 6rem 2rem;\r\n  background: white;\r\n}\r\n\r\n.teamGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 3rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.teamMember {\r\n  text-align: center;\r\n  background: var(--background-light);\r\n  padding: 2rem;\r\n  border-radius: 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.teamMember:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n.teamPhoto {\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  margin-bottom: 1.5rem;\r\n  border: 4px solid var(--primary-color);\r\n}\r\n\r\n.teamMember h3 {\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  color: var(--text-dark);\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.teamRole {\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  color: var(--primary-color);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.teamMember p:not(.teamRole) {\r\n  color: var(--text-light);\r\n  line-height: 1.6;\r\n}\r\n\r\n/* CTA Section */\r\n.ctaSection {\r\n  padding: 4rem 2rem;\r\n  background: var(--primary-color);\r\n  color: white;\r\n  text-align: center;\r\n}\r\n\r\n.ctaContent h2 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.ctaContent p {\r\n  font-size: 1.1rem;\r\n  margin-bottom: 2rem;\r\n  opacity: 0.9;\r\n}\r\n\r\n.ctaBtn {\r\n  padding: 1rem 2rem;\r\n  background: white;\r\n  color: var(--primary-color);\r\n  border: none;\r\n  border-radius: 50px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.ctaBtn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n/* Footer */\r\n.footer {\r\n  background: var(--text-dark);\r\n  color: white;\r\n  padding: 3rem 2rem 1rem;\r\n}\r\n\r\n.footerContent {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.footerSection h3 {\r\n  font-size: 1.3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerSection h4 {\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerSection p {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  line-height: 1.6;\r\n}\r\n\r\n.footerSection ul {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.footerSection ul li {\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.footerSection ul li a {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-decoration: none;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.footerSection ul li a:hover {\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerBottom {\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  padding-top: 1rem;\r\n  text-align: center;\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .heroTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .navMenu {\r\n    display: none;\r\n  }\r\n\r\n  .valuesGrid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .teamGrid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .storyContent h2 {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAEA;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AAQA;;;;AAKA;;;;;;;;;;;AAWA;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;EACE;;;;;;AAMF;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;;;AAMA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;EAQA", "debugId": null}}]}