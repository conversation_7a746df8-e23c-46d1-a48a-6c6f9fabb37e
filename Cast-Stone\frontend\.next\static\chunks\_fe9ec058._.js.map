{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/contact/page.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"page-module__OSLHOG__active\",\n  \"contactContainer\": \"page-module__OSLHOG__contactContainer\",\n  \"contactInfo\": \"page-module__OSLHOG__contactInfo\",\n  \"contactSection\": \"page-module__OSLHOG__contactSection\",\n  \"container\": \"page-module__OSLHOG__container\",\n  \"footer\": \"page-module__OSLHOG__footer\",\n  \"footerBottom\": \"page-module__OSLHOG__footerBottom\",\n  \"footerContent\": \"page-module__OSLHOG__footerContent\",\n  \"footerSection\": \"page-module__OSLHOG__footerSection\",\n  \"form\": \"page-module__OSLHOG__form\",\n  \"formContainer\": \"page-module__OSLHOG__formContainer\",\n  \"formGroup\": \"page-module__OSLHOG__formGroup\",\n  \"formRow\": \"page-module__OSLHOG__formRow\",\n  \"hero\": \"page-module__OSLHOG__hero\",\n  \"heroContent\": \"page-module__OSLHOG__heroContent\",\n  \"heroSubtitle\": \"page-module__OSLHOG__heroSubtitle\",\n  \"heroTitle\": \"page-module__OSLHOG__heroTitle\",\n  \"infoGrid\": \"page-module__OSLHOG__infoGrid\",\n  \"infoIcon\": \"page-module__OSLHOG__infoIcon\",\n  \"infoItem\": \"page-module__OSLHOG__infoItem\",\n  \"infoSubtext\": \"page-module__OSLHOG__infoSubtext\",\n  \"infoText\": \"page-module__OSLHOG__infoText\",\n  \"infoTitle\": \"page-module__OSLHOG__infoTitle\",\n  \"input\": \"page-module__OSLHOG__input\",\n  \"label\": \"page-module__OSLHOG__label\",\n  \"logo\": \"page-module__OSLHOG__logo\",\n  \"navContainer\": \"page-module__OSLHOG__navContainer\",\n  \"navMenu\": \"page-module__OSLHOG__navMenu\",\n  \"navigation\": \"page-module__OSLHOG__navigation\",\n  \"select\": \"page-module__OSLHOG__select\",\n  \"submitButton\": \"page-module__OSLHOG__submitButton\",\n  \"textarea\": \"page-module__OSLHOG__textarea\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/contact/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport styles from \"./page.module.css\";\r\n\r\nexport default function Contact() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    phone: '',\r\n    projectType: '',\r\n    message: ''\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    // Handle form submission here\r\n    console.log('Form submitted:', formData);\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Navigation */}\r\n      <nav className={styles.navigation}>\r\n        <div className={styles.navContainer}>\r\n          <div className={styles.logo}>\r\n            <h1>Cast Stone</h1>\r\n            <span>Interiors & Decorations</span>\r\n          </div>\r\n          <ul className={styles.navMenu}>\r\n            <li><a href=\"/\">Home</a></li>\r\n            <li><a href=\"/products\">Products</a></li>\r\n            <li><a href=\"/gallery\">Gallery</a></li>\r\n            <li><a href=\"/about\">About</a></li>\r\n            <li><a href=\"/contact\" className={styles.active}>Contact</a></li>\r\n          </ul>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Hero Section */}\r\n      <section className={styles.hero}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>Contact Us</h1>\r\n          <p className={styles.heroSubtitle}>\r\n            Ready to transform your space? Get in touch with our team of experts\r\n            to discuss your cast stone project.\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section className={styles.contactSection}>\r\n        <div className={styles.contactContainer}>\r\n          {/* Contact Form */}\r\n          <div className={styles.formContainer}>\r\n            <h2>Start Your Project</h2>\r\n            <p>Fill out the form below and we'll get back to you within 24 hours.</p>\r\n\r\n            <form className={styles.form} onSubmit={handleSubmit}>\r\n              <div className={styles.formRow}>\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"name\" className={styles.label}>\r\n                    Full Name *\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id=\"name\"\r\n                    name=\"name\"\r\n                    className={styles.input}\r\n                    placeholder=\"Your full name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"email\" className={styles.label}>\r\n                    Email Address *\r\n                  </label>\r\n                  <input\r\n                    type=\"email\"\r\n                    id=\"email\"\r\n                    name=\"email\"\r\n                    className={styles.input}\r\n                    placeholder=\"<EMAIL>\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.formRow}>\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"phone\" className={styles.label}>\r\n                    Phone Number\r\n                  </label>\r\n                  <input\r\n                    type=\"tel\"\r\n                    id=\"phone\"\r\n                    name=\"phone\"\r\n                    className={styles.input}\r\n                    placeholder=\"(*************\"\r\n                    value={formData.phone}\r\n                    onChange={handleChange}\r\n                  />\r\n                </div>\r\n\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"projectType\" className={styles.label}>\r\n                    Project Type\r\n                  </label>\r\n                  <select\r\n                    id=\"projectType\"\r\n                    name=\"projectType\"\r\n                    className={styles.select}\r\n                    value={formData.projectType}\r\n                    onChange={handleChange}\r\n                  >\r\n                    <option value=\"\">Select project type</option>\r\n                    <option value=\"fireplace\">Fireplace</option>\r\n                    <option value=\"garden\">Garden Features</option>\r\n                    <option value=\"architectural\">Architectural Elements</option>\r\n                    <option value=\"restoration\">Restoration</option>\r\n                    <option value=\"custom\">Custom Design</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.formGroup}>\r\n                <label htmlFor=\"message\" className={styles.label}>\r\n                  Project Details *\r\n                </label>\r\n                <textarea\r\n                  id=\"message\"\r\n                  name=\"message\"\r\n                  rows={6}\r\n                  className={styles.textarea}\r\n                  placeholder=\"Tell us about your project, timeline, and any specific requirements...\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <button type=\"submit\" className={styles.submitButton}>\r\n                Send Message\r\n              </button>\r\n            </form>\r\n          </div>\r\n\r\n          {/* Contact Information */}\r\n          <div className={styles.contactInfo}>\r\n            <h2>Get In Touch</h2>\r\n            <p>We're here to help bring your vision to life.</p>\r\n\r\n            <div className={styles.infoGrid}>\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>📍</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Visit Our Workshop</h3>\r\n                  <p className={styles.infoText}>\r\n                    123 Artisan Way<br />\r\n                    Craftsman City, CC 12345\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>📞</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Call Us</h3>\r\n                  <p className={styles.infoText}>(*************</p>\r\n                  <p className={styles.infoSubtext}>Mon-Fri 8AM-6PM</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>✉️</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Email Us</h3>\r\n                  <p className={styles.infoText}><EMAIL></p>\r\n                  <p className={styles.infoSubtext}>We respond within 24 hours</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>🕒</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Business Hours</h3>\r\n                  <p className={styles.infoText}>\r\n                    Monday - Friday: 8:00 AM - 6:00 PM<br />\r\n                    Saturday: 9:00 AM - 4:00 PM<br />\r\n                    Sunday: Closed\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className={styles.footer}>\r\n        <div className={styles.footerContent}>\r\n          <div className={styles.footerSection}>\r\n            <h3>Cast Stone</h3>\r\n            <p>Creating timeless beauty with handcrafted cast stone elements for over 25 years.</p>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Quick Links</h4>\r\n            <ul>\r\n              <li><a href=\"/products\">Products</a></li>\r\n              <li><a href=\"/gallery\">Gallery</a></li>\r\n              <li><a href=\"/about\">About Us</a></li>\r\n              <li><a href=\"/contact\">Contact</a></li>\r\n            </ul>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Contact Info</h4>\r\n            <p>123 Artisan Way<br />Craftsman City, CC 12345</p>\r\n            <p>Phone: (*************</p>\r\n            <p>Email: <EMAIL></p>\r\n          </div>\r\n        </div>\r\n        <div className={styles.footerBottom}>\r\n          <p>&copy; 2024 Cast Stone Interiors. All rights reserved.</p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,6LAAC;gBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,YAAY;;sCACjC,6LAAC;4BAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;4BAAG,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;8CAC3B,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAI;;;;;;;;;;;8CAChB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAY;;;;;;;;;;;8CACxB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;8CACvB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;8CACrB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;wCAAW,WAAW,4IAAA,CAAA,UAAM,CAAC,MAAM;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,6LAAC;gBAAQ,WAAW,4IAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,6LAAC;oBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;;sCAChC,6LAAC;4BAAG,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;sCAAE;;;;;;sCACjC,6LAAC;4BAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;;;;;;;;;;;;0BAQvC,6LAAC;gBAAQ,WAAW,4IAAA,CAAA,UAAM,CAAC,cAAc;0BACvC,cAAA,6LAAC;oBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,gBAAgB;;sCAErC,6LAAC;4BAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,aAAa;;8CAClC,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAE;;;;;;8CAEH,6LAAC;oCAAK,WAAW,4IAAA,CAAA,UAAM,CAAC,IAAI;oCAAE,UAAU;;sDACtC,6LAAC;4CAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,6LAAC;4DAAM,SAAQ;4DAAO,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAG/C,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;4DACvB,aAAY;4DACZ,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,6LAAC;4DAAM,SAAQ;4DAAQ,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAGhD,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;4DACvB,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,6LAAC;4DAAM,SAAQ;4DAAQ,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAGhD,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;4DACvB,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU;;;;;;;;;;;;8DAId,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,6LAAC;4DAAM,SAAQ;4DAAc,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAGtD,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,WAAW,4IAAA,CAAA,UAAM,CAAC,MAAM;4DACxB,OAAO,SAAS,WAAW;4DAC3B,UAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,6LAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,6LAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,6LAAC;oEAAO,OAAM;8EAAgB;;;;;;8EAC9B,6LAAC;oEAAO,OAAM;8EAAc;;;;;;8EAC5B,6LAAC;oEAAO,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAK7B,6LAAC;4CAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,6LAAC;oDAAM,SAAQ;oDAAU,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;8DAAE;;;;;;8DAGlD,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;oDAC1B,aAAY;oDACZ,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;4CAAO,MAAK;4CAAS,WAAW,4IAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;;;;;;;;;;;;;sCAO1D,6LAAC;4BAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;;8CAChC,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAE;;;;;;8CAEH,6LAAC;oCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC7B,6LAAC;4CAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,6LAAC;4DAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;gEAAE;8EACd,6LAAC;;;;;gEAAK;;;;;;;;;;;;;;;;;;;sDAM3B,6LAAC;4CAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,6LAAC;4DAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;sEAAE;;;;;;sEAC/B,6LAAC;4DAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;sEAAE;;;;;;;;;;;;;;;;;;sDAItC,6LAAC;4CAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,6LAAC;4DAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;sEAAE;;;;;;sEAC/B,6LAAC;4DAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;sEAAE;;;;;;;;;;;;;;;;;;sDAItC,6LAAC;4CAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,6LAAC;oDAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,6LAAC;;sEACC,6LAAC;4DAAG,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,6LAAC;4DAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;gEAAE;8EACK,6LAAC;;;;;gEAAK;8EACb,6LAAC;;;;;gEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW/C,6LAAC;gBAAO,WAAW,4IAAA,CAAA,UAAM,CAAC,MAAM;;kCAC9B,6LAAC;wBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;gCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAE;;;;;;;;;;;;0CAEL,6LAAC;gCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;;0DACC,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAY;;;;;;;;;;;0DACxB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;0DACvB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAS;;;;;;;;;;;0DACrB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;0CAG3B,6LAAC;gCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;;4CAAE;0DAAe,6LAAC;;;;;4CAAK;;;;;;;kDACxB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAGP,6LAAC;wBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,YAAY;kCACjC,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb;GA1OwB;KAAA", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}