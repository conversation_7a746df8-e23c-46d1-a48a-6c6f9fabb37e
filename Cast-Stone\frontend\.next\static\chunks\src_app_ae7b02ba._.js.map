{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/page.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"page-module___8aEwW__active\",\n  \"activeDot\": \"page-module___8aEwW__activeDot\",\n  \"container\": \"page-module___8aEwW__container\",\n  \"emailInput\": \"page-module___8aEwW__emailInput\",\n  \"featuredProducts\": \"page-module___8aEwW__featuredProducts\",\n  \"footer\": \"page-module___8aEwW__footer\",\n  \"footerBottom\": \"page-module___8aEwW__footerBottom\",\n  \"footerContent\": \"page-module___8aEwW__footerContent\",\n  \"footerSection\": \"page-module___8aEwW__footerSection\",\n  \"hero\": \"page-module___8aEwW__hero\",\n  \"heroActions\": \"page-module___8aEwW__heroActions\",\n  \"heroContent\": \"page-module___8aEwW__heroContent\",\n  \"heroImage\": \"page-module___8aEwW__heroImage\",\n  \"heroImg\": \"page-module___8aEwW__heroImg\",\n  \"heroSubtitle\": \"page-module___8aEwW__heroSubtitle\",\n  \"heroTitle\": \"page-module___8aEwW__heroTitle\",\n  \"highlight\": \"page-module___8aEwW__highlight\",\n  \"logo\": \"page-module___8aEwW__logo\",\n  \"navContainer\": \"page-module___8aEwW__navContainer\",\n  \"navMenu\": \"page-module___8aEwW__navMenu\",\n  \"navigation\": \"page-module___8aEwW__navigation\",\n  \"newsletter\": \"page-module___8aEwW__newsletter\",\n  \"newsletterContent\": \"page-module___8aEwW__newsletterContent\",\n  \"newsletterForm\": \"page-module___8aEwW__newsletterForm\",\n  \"playButton\": \"page-module___8aEwW__playButton\",\n  \"primaryBtn\": \"page-module___8aEwW__primaryBtn\",\n  \"productCard\": \"page-module___8aEwW__productCard\",\n  \"productGrid\": \"page-module___8aEwW__productGrid\",\n  \"productImage\": \"page-module___8aEwW__productImage\",\n  \"productInfo\": \"page-module___8aEwW__productInfo\",\n  \"productPrice\": \"page-module___8aEwW__productPrice\",\n  \"secondaryBtn\": \"page-module___8aEwW__secondaryBtn\",\n  \"sectionHeader\": \"page-module___8aEwW__sectionHeader\",\n  \"slideFromLeft\": \"page-module___8aEwW__slideFromLeft\",\n  \"slideFromRight\": \"page-module___8aEwW__slideFromRight\",\n  \"subscribeBtn\": \"page-module___8aEwW__subscribeBtn\",\n  \"videoCarousel\": \"page-module___8aEwW__videoCarousel\",\n  \"videoContainer\": \"page-module___8aEwW__videoContainer\",\n  \"videoControls\": \"page-module___8aEwW__videoControls\",\n  \"videoDot\": \"page-module___8aEwW__videoDot\",\n  \"videoInfo\": \"page-module___8aEwW__videoInfo\",\n  \"videoPlaceholder\": \"page-module___8aEwW__videoPlaceholder\",\n  \"videoSection\": \"page-module___8aEwW__videoSection\",\n  \"videoSlide\": \"page-module___8aEwW__videoSlide\",\n  \"videoThumbnail\": \"page-module___8aEwW__videoThumbnail\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Image from \"next/image\";\r\nimport styles from \"./page.module.css\";\r\n\r\nexport default function Home() {\r\n  const [currentVideo, setCurrentVideo] = useState(0);\r\n\r\n  const videos = [\r\n    {\r\n      id: 1,\r\n      title: \"Luxury Cast Stone Fireplaces\",\r\n      description: \"Transform your living space with our handcrafted cast stone fireplaces\",\r\n      videoUrl: \"/videos/fireplace-demo.mp4\", // Placeholder\r\n      thumbnail: \"/images/fireplace-thumb.jpg\"\r\n    },\r\n    {\r\n      id: 2,\r\n      title: \"Elegant Garden Features\",\r\n      description: \"Create stunning outdoor spaces with our cast stone garden elements\",\r\n      videoUrl: \"/videos/garden-demo.mp4\", // Placeholder\r\n      thumbnail: \"/images/garden-thumb.jpg\"\r\n    },\r\n    {\r\n      id: 3,\r\n      title: \"Architectural Details\",\r\n      description: \"Add sophistication with our custom cast stone architectural elements\",\r\n      videoUrl: \"/videos/architecture-demo.mp4\", // Placeholder\r\n      thumbnail: \"/images/architecture-thumb.jpg\"\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setCurrentVideo((prev) => (prev + 1) % videos.length);\r\n    }, 5000);\r\n    return () => clearInterval(interval);\r\n  }, [videos.length]);\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Navigation */}\r\n      <nav className={styles.navigation}>\r\n        <div className={styles.navContainer}>\r\n          <div className={styles.logo}>\r\n            <h1>Cast Stone</h1>\r\n            <span>Interiors & Decorations</span>\r\n          </div>\r\n          <ul className={styles.navMenu}>\r\n            <li><a href=\"/\">Home</a></li>\r\n            <li><a href=\"/products\">Products</a></li>\r\n            <li><a href=\"/gallery\">Gallery</a></li>\r\n            <li><a href=\"/about\">About</a></li>\r\n            <li><a href=\"/contact\">Contact</a></li>\r\n          </ul>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Hero Section */}\r\n      <section className={styles.hero}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>\r\n            Timeless Elegance in\r\n            <span className={styles.highlight}> Cast Stone</span>\r\n          </h1>\r\n          <p className={styles.heroSubtitle}>\r\n            Discover our exquisite collection of handcrafted cast stone interiors,\r\n            fireplaces, and decorative elements that transform spaces into works of art.\r\n          </p>\r\n          <div className={styles.heroActions}>\r\n            <button className={styles.primaryBtn}>Explore Collection</button>\r\n            <button className={styles.secondaryBtn}>Watch Our Story</button>\r\n          </div>\r\n        </div>\r\n        <div className={styles.heroImage}>\r\n          <Image\r\n            src=\"/images/hero-cast-stone.jpg\"\r\n            alt=\"Luxury Cast Stone Interior\"\r\n            width={800}\r\n            height={600}\r\n            priority\r\n            className={styles.heroImg}\r\n          />\r\n        </div>\r\n      </section>\r\n\r\n      {/* Dynamic Video Carousel */}\r\n      <section className={styles.videoSection}>\r\n        <div className={styles.sectionHeader}>\r\n          <h2>Experience Our Craftsmanship</h2>\r\n          <p>Watch how we transform spaces with our premium cast stone creations</p>\r\n        </div>\r\n\r\n        <div className={styles.videoCarousel}>\r\n          {videos.map((video, index) => (\r\n            <div\r\n              key={video.id}\r\n              className={`${styles.videoSlide} ${\r\n                index === currentVideo ? styles.active : ''\r\n              } ${\r\n                index % 2 === 0 ? styles.slideFromLeft : styles.slideFromRight\r\n              }`}\r\n            >\r\n              <div className={styles.videoContainer}>\r\n                <div className={styles.videoPlaceholder}>\r\n                  <div className={styles.playButton}>\r\n                    <svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\">\r\n                      <circle cx=\"30\" cy=\"30\" r=\"30\" fill=\"rgba(255,255,255,0.9)\" />\r\n                      <polygon points=\"24,18 24,42 42,30\" fill=\"#333\" />\r\n                    </svg>\r\n                  </div>\r\n                  <Image\r\n                    src={video.thumbnail || \"/images/placeholder-video.jpg\"}\r\n                    alt={video.title}\r\n                    width={600}\r\n                    height={400}\r\n                    className={styles.videoThumbnail}\r\n                  />\r\n                </div>\r\n                <div className={styles.videoInfo}>\r\n                  <h3>{video.title}</h3>\r\n                  <p>{video.description}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className={styles.videoControls}>\r\n          {videos.map((_, index) => (\r\n            <button\r\n              key={index}\r\n              className={`${styles.videoDot} ${\r\n                index === currentVideo ? styles.activeDot : ''\r\n              }`}\r\n              onClick={() => setCurrentVideo(index)}\r\n            />\r\n          ))}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Featured Products */}\r\n      <section className={styles.featuredProducts}>\r\n        <div className={styles.sectionHeader}>\r\n          <h2>Featured Collections</h2>\r\n          <p>Discover our most popular cast stone pieces</p>\r\n        </div>\r\n\r\n        <div className={styles.productGrid}>\r\n          <div className={styles.productCard}>\r\n            <Image\r\n              src=\"/images/fireplace-collection.jpg\"\r\n              alt=\"Fireplace Collection\"\r\n              width={400}\r\n              height={300}\r\n              className={styles.productImage}\r\n            />\r\n            <div className={styles.productInfo}>\r\n              <h3>Fireplace Collection</h3>\r\n              <p>Handcrafted mantels and surrounds</p>\r\n              <span className={styles.productPrice}>From $2,500</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.productCard}>\r\n            <Image\r\n              src=\"/images/garden-collection.jpg\"\r\n              alt=\"Garden Collection\"\r\n              width={400}\r\n              height={300}\r\n              className={styles.productImage}\r\n            />\r\n            <div className={styles.productInfo}>\r\n              <h3>Garden Features</h3>\r\n              <p>Fountains, planters, and sculptures</p>\r\n              <span className={styles.productPrice}>From $800</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.productCard}>\r\n            <Image\r\n              src=\"/images/architectural-collection.jpg\"\r\n              alt=\"Architectural Collection\"\r\n              width={400}\r\n              height={300}\r\n              className={styles.productImage}\r\n            />\r\n            <div className={styles.productInfo}>\r\n              <h3>Architectural Elements</h3>\r\n              <p>Columns, balustrades, and moldings</p>\r\n              <span className={styles.productPrice}>From $1,200</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Newsletter Section */}\r\n      <section className={styles.newsletter}>\r\n        <div className={styles.newsletterContent}>\r\n          <h2>Stay Inspired</h2>\r\n          <p>Get the latest design trends and exclusive offers delivered to your inbox</p>\r\n          <div className={styles.newsletterForm}>\r\n            <input\r\n              type=\"email\"\r\n              placeholder=\"Enter your email address\"\r\n              className={styles.emailInput}\r\n            />\r\n            <button className={styles.subscribeBtn}>Subscribe</button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className={styles.footer}>\r\n        <div className={styles.footerContent}>\r\n          <div className={styles.footerSection}>\r\n            <h3>Cast Stone</h3>\r\n            <p>Creating timeless beauty with handcrafted cast stone elements for over 25 years.</p>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Quick Links</h4>\r\n            <ul>\r\n              <li><a href=\"/products\">Products</a></li>\r\n              <li><a href=\"/gallery\">Gallery</a></li>\r\n              <li><a href=\"/about\">About Us</a></li>\r\n              <li><a href=\"/contact\">Contact</a></li>\r\n            </ul>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Contact Info</h4>\r\n            <p>123 Artisan Way<br />Craftsman City, CC 12345</p>\r\n            <p>Phone: (*************</p>\r\n            <p>Email: <EMAIL></p>\r\n          </div>\r\n        </div>\r\n        <div className={styles.footerBottom}>\r\n          <p>&copy; 2024 Cast Stone Interiors. All rights reserved.</p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,SAAS;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,WAAW;QACb;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,WAAW;2CAAY;oBAC3B;mDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;;gBACtD;0CAAG;YACH;kCAAO,IAAM,cAAc;;QAC7B;yBAAG;QAAC,OAAO,MAAM;KAAC;IAElB,qBACE,6LAAC;QAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,6LAAC;gBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;;sCACjC,6LAAC;4BAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;4BAAG,WAAW,iIAAA,CAAA,UAAM,CAAC,OAAO;;8CAC3B,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAI;;;;;;;;;;;8CAChB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAY;;;;;;;;;;;8CACxB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;8CACvB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;8CACrB,6LAAC;8CAAG,cAAA,6LAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAQ,WAAW,iIAAA,CAAA,UAAM,CAAC,IAAI;;kCAC7B,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,6LAAC;gCAAG,WAAW,iIAAA,CAAA,UAAM,CAAC,SAAS;;oCAAE;kDAE/B,6LAAC;wCAAK,WAAW,iIAAA,CAAA,UAAM,CAAC,SAAS;kDAAE;;;;;;;;;;;;0CAErC,6LAAC;gCAAE,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;0CAAE;;;;;;0CAInC,6LAAC;gCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;wCAAO,WAAW,iIAAA,CAAA,UAAM,CAAC,UAAU;kDAAE;;;;;;kDACtC,6LAAC;wCAAO,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;kDAAE;;;;;;;;;;;;;;;;;;kCAG5C,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,SAAS;kCAC9B,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,QAAQ;4BACR,WAAW,iIAAA,CAAA,UAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;0BAM/B,6LAAC;gBAAQ,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;;kCACrC,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAE;;;;;;;;;;;;kCAGL,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;kCACjC,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;gCAEC,WAAW,GAAG,iIAAA,CAAA,UAAM,CAAC,UAAU,CAAC,CAAC,EAC/B,UAAU,eAAe,iIAAA,CAAA,UAAM,CAAC,MAAM,GAAG,GAC1C,CAAC,EACA,QAAQ,MAAM,IAAI,iIAAA,CAAA,UAAM,CAAC,aAAa,GAAG,iIAAA,CAAA,UAAM,CAAC,cAAc,EAC9D;0CAEF,cAAA,6LAAC;oCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,6LAAC;4CAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,gBAAgB;;8DACrC,6LAAC;oDAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,UAAU;8DAC/B,cAAA,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;;0EAClC,6LAAC;gEAAO,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,MAAK;;;;;;0EACpC,6LAAC;gEAAQ,QAAO;gEAAoB,MAAK;;;;;;;;;;;;;;;;;8DAG7C,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,SAAS,IAAI;oDACxB,KAAK,MAAM,KAAK;oDAChB,OAAO;oDACP,QAAQ;oDACR,WAAW,iIAAA,CAAA,UAAM,CAAC,cAAc;;;;;;;;;;;;sDAGpC,6LAAC;4CAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,6LAAC;8DAAI,MAAM,KAAK;;;;;;8DAChB,6LAAC;8DAAG,MAAM,WAAW;;;;;;;;;;;;;;;;;;+BAzBpB,MAAM,EAAE;;;;;;;;;;kCAgCnB,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;kCACjC,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;gCAEC,WAAW,GAAG,iIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,CAAC,EAC7B,UAAU,eAAe,iIAAA,CAAA,UAAM,CAAC,SAAS,GAAG,IAC5C;gCACF,SAAS,IAAM,gBAAgB;+BAJ1B;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAQ,WAAW,iIAAA,CAAA,UAAM,CAAC,gBAAgB;;kCACzC,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAE;;;;;;;;;;;;kCAGL,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,6LAAC;gCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDAEhC,6LAAC;wCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAK,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;0DAAE;;;;;;;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDAEhC,6LAAC;wCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAK,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;0DAAE;;;;;;;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDAEhC,6LAAC;wCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAK,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAQ,WAAW,iIAAA,CAAA,UAAM,CAAC,UAAU;0BACnC,cAAA,6LAAC;oBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,iBAAiB;;sCACtC,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,cAAc;;8CACnC,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAW,iIAAA,CAAA,UAAM,CAAC,UAAU;;;;;;8CAE9B,6LAAC;oCAAO,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;8CAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,6LAAC;gBAAO,WAAW,iIAAA,CAAA,UAAM,CAAC,MAAM;;kCAC9B,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;gCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAE;;;;;;;;;;;;0CAEL,6LAAC;gCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;;0DACC,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAY;;;;;;;;;;;0DACxB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;0DACvB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAS;;;;;;;;;;;0DACrB,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;0CAG3B,6LAAC;gCAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;;4CAAE;0DAAe,6LAAC;;;;;4CAAK;;;;;;;kDACxB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAGP,6LAAC;wBAAI,WAAW,iIAAA,CAAA,UAAM,CAAC,YAAY;kCACjC,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb;GA5OwB;KAAA", "debugId": null}}]}