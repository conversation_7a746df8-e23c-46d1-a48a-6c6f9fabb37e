/* Home page specific styles */
.container {
  display: grid;
  grid-template-rows: 20px 1fr 20px;
  align-items: center;
  justify-items: center;
  min-height: 100vh;
  padding: 2rem;
  padding-bottom: 5rem;
  gap: 4rem;
  font-family: var(--font-geist-sans);
}

@media (min-width: 640px) {
  .container {
    padding: 5rem;
  }
}

.main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  grid-row-start: 2;
  align-items: center;
}

@media (min-width: 640px) {
  .main {
    align-items: flex-start;
  }
}

.logo {
  filter: invert(0);
}

.logo[data-theme="dark"] {
  filter: invert(1);
}

.instructions {
  list-style-position: inside;
  list-style-type: decimal;
  font-size: 0.875rem;
  line-height: 1.5;
  text-align: center;
  font-family: var(--font-geist-mono);
}

@media (min-width: 640px) {
  .instructions {
    text-align: left;
  }
}

.instructionItem {
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}

.code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: var(--font-geist-mono);
  font-weight: 600;
}

.code[data-theme="dark"] {
  background-color: rgba(255, 255, 255, 0.06);
}

.actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-direction: column;
}

@media (min-width: 640px) {
  .actions {
    flex-direction: row;
  }
}

.primaryButton {
  border-radius: 9999px;
  border: 1px solid transparent;
  transition: colors 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--foreground);
  color: var(--background);
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  height: 2.5rem;
  padding: 0 1rem;
  text-decoration: none;
}

@media (min-width: 640px) {
  .primaryButton {
    font-size: 1rem;
    height: 3rem;
    padding: 0 1.25rem;
    width: auto;
  }
}

.primaryButton:hover {
  background-color: #383838;
}

.primaryButton:hover[data-theme="dark"] {
  background-color: #ccc;
}

.secondaryButton {
  border-radius: 9999px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: colors 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 0.875rem;
  height: 2.5rem;
  padding: 0 1rem;
  width: 100%;
  text-decoration: none;
  color: inherit;
}

@media (min-width: 640px) {
  .secondaryButton {
    font-size: 1rem;
    height: 3rem;
    padding: 0 1.25rem;
    width: auto;
  }
}

@media (min-width: 768px) {
  .secondaryButton {
    width: 158px;
  }
}

.secondaryButton[data-theme="dark"] {
  border-color: rgba(255, 255, 255, 0.145);
}

.secondaryButton:hover {
  background-color: #f2f2f2;
  border-color: transparent;
}

.secondaryButton:hover[data-theme="dark"] {
  background-color: #1a1a1a;
}

.footer {
  grid-row-start: 3;
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.footerLink {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: inherit;
  text-decoration: none;
}

.footerLink:hover {
  text-decoration: underline;
  text-underline-offset: 4px;
}
