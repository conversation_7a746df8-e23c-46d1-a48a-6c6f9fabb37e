{"version": 3, "sources": [], "sections": [{"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/gallery/page.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"page-module__8IDnjq__active\",\n  \"categoryBtn\": \"page-module__8IDnjq__categoryBtn\",\n  \"categoryFilter\": \"page-module__8IDnjq__categoryFilter\",\n  \"closeBtn\": \"page-module__8IDnjq__closeBtn\",\n  \"container\": \"page-module__8IDnjq__container\",\n  \"ctaBtn\": \"page-module__8IDnjq__ctaBtn\",\n  \"ctaContent\": \"page-module__8IDnjq__ctaContent\",\n  \"ctaSection\": \"page-module__8IDnjq__ctaSection\",\n  \"footer\": \"page-module__8IDnjq__footer\",\n  \"footerBottom\": \"page-module__8IDnjq__footerBottom\",\n  \"footerContent\": \"page-module__8IDnjq__footerContent\",\n  \"footerSection\": \"page-module__8IDnjq__footerSection\",\n  \"galleryGrid\": \"page-module__8IDnjq__galleryGrid\",\n  \"galleryImage\": \"page-module__8IDnjq__galleryImage\",\n  \"galleryItem\": \"page-module__8IDnjq__galleryItem\",\n  \"gallerySection\": \"page-module__8IDnjq__gallerySection\",\n  \"hero\": \"page-module__8IDnjq__hero\",\n  \"heroContent\": \"page-module__8IDnjq__heroContent\",\n  \"heroSubtitle\": \"page-module__8IDnjq__heroSubtitle\",\n  \"heroTitle\": \"page-module__8IDnjq__heroTitle\",\n  \"imageContainer\": \"page-module__8IDnjq__imageContainer\",\n  \"imageDescription\": \"page-module__8IDnjq__imageDescription\",\n  \"imageOverlay\": \"page-module__8IDnjq__imageOverlay\",\n  \"imageTitle\": \"page-module__8IDnjq__imageTitle\",\n  \"logo\": \"page-module__8IDnjq__logo\",\n  \"modal\": \"page-module__8IDnjq__modal\",\n  \"modalContent\": \"page-module__8IDnjq__modalContent\",\n  \"modalImage\": \"page-module__8IDnjq__modalImage\",\n  \"modalInfo\": \"page-module__8IDnjq__modalInfo\",\n  \"navContainer\": \"page-module__8IDnjq__navContainer\",\n  \"navMenu\": \"page-module__8IDnjq__navMenu\",\n  \"navigation\": \"page-module__8IDnjq__navigation\",\n  \"viewBtn\": \"page-module__8IDnjq__viewBtn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/gallery/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Image from \"next/image\";\r\nimport styles from \"./page.module.css\";\r\n\r\nexport default function Gallery() {\r\n  const [selectedCategory, setSelectedCategory] = useState('all');\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n\r\n  const categories = [\r\n    { id: 'all', name: 'All Projects' },\r\n    { id: 'residential', name: 'Residential' },\r\n    { id: 'commercial', name: 'Commercial' },\r\n    { id: 'garden', name: 'Garden & Outdoor' },\r\n    { id: 'restoration', name: 'Restoration' }\r\n  ];\r\n\r\n  const galleryItems = [\r\n    {\r\n      id: 1,\r\n      title: \"Luxury Living Room Fireplace\",\r\n      category: \"residential\",\r\n      image: \"/images/fireplace-collection.jpg\",\r\n      description: \"Custom cast stone fireplace with intricate carved details\"\r\n    },\r\n    {\r\n      id: 2,\r\n      title: \"Grand Hotel Entrance\",\r\n      category: \"commercial\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Majestic columns and architectural elements for hotel lobby\"\r\n    },\r\n    {\r\n      id: 3,\r\n      title: \"Private Garden Fountain\",\r\n      category: \"garden\",\r\n      image: \"/images/garden-collection.jpg\",\r\n      description: \"Three-tier fountain centerpiece for estate garden\"\r\n    },\r\n    {\r\n      id: 4,\r\n      title: \"Historic Building Restoration\",\r\n      category: \"restoration\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Faithful reproduction of original cast stone elements\"\r\n    },\r\n    {\r\n      id: 5,\r\n      title: \"Modern Fireplace Design\",\r\n      category: \"residential\",\r\n      image: \"/images/fireplace-collection.jpg\",\r\n      description: \"Contemporary cast stone fireplace with clean lines\"\r\n    },\r\n    {\r\n      id: 6,\r\n      title: \"Corporate Headquarters\",\r\n      category: \"commercial\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Impressive facade elements for corporate building\"\r\n    },\r\n    {\r\n      id: 7,\r\n      title: \"Estate Garden Features\",\r\n      category: \"garden\",\r\n      image: \"/images/garden-collection.jpg\",\r\n      description: \"Complete garden transformation with cast stone elements\"\r\n    },\r\n    {\r\n      id: 8,\r\n      title: \"Victorian Home Restoration\",\r\n      category: \"restoration\",\r\n      image: \"/images/architectural-collection.jpg\",\r\n      description: \"Period-accurate cast stone details for historic home\"\r\n    },\r\n    {\r\n      id: 9,\r\n      title: \"Luxury Master Suite\",\r\n      category: \"residential\",\r\n      image: \"/images/fireplace-collection.jpg\",\r\n      description: \"Elegant bedroom fireplace with custom surround\"\r\n    }\r\n  ];\r\n\r\n  const filteredItems = selectedCategory === 'all' \r\n    ? galleryItems \r\n    : galleryItems.filter(item => item.category === selectedCategory);\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Navigation */}\r\n      <nav className={styles.navigation}>\r\n        <div className={styles.navContainer}>\r\n          <div className={styles.logo}>\r\n            <h1>Cast Stone</h1>\r\n            <span>Interiors & Decorations</span>\r\n          </div>\r\n          <ul className={styles.navMenu}>\r\n            <li><a href=\"/\">Home</a></li>\r\n            <li><a href=\"/products\">Products</a></li>\r\n            <li><a href=\"/gallery\" className={styles.active}>Gallery</a></li>\r\n            <li><a href=\"/about\">About</a></li>\r\n            <li><a href=\"/contact\">Contact</a></li>\r\n          </ul>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Hero Section */}\r\n      <section className={styles.hero}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>Project Gallery</h1>\r\n          <p className={styles.heroSubtitle}>\r\n            Explore our portfolio of stunning cast stone installations, \r\n            from intimate residential fireplaces to grand commercial projects.\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Gallery Section */}\r\n      <section className={styles.gallerySection}>\r\n        <div className={styles.categoryFilter}>\r\n          {categories.map(category => (\r\n            <button\r\n              key={category.id}\r\n              className={`${styles.categoryBtn} ${\r\n                selectedCategory === category.id ? styles.active : ''\r\n              }`}\r\n              onClick={() => setSelectedCategory(category.id)}\r\n            >\r\n              {category.name}\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Gallery Grid */}\r\n        <div className={styles.galleryGrid}>\r\n          {filteredItems.map(item => (\r\n            <div \r\n              key={item.id} \r\n              className={styles.galleryItem}\r\n              onClick={() => setSelectedImage(item)}\r\n            >\r\n              <div className={styles.imageContainer}>\r\n                <Image\r\n                  src={item.image}\r\n                  alt={item.title}\r\n                  width={400}\r\n                  height={300}\r\n                  className={styles.galleryImage}\r\n                />\r\n                <div className={styles.imageOverlay}>\r\n                  <h3 className={styles.imageTitle}>{item.title}</h3>\r\n                  <p className={styles.imageDescription}>{item.description}</p>\r\n                  <button className={styles.viewBtn}>View Details</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Modal for enlarged image */}\r\n      {selectedImage && (\r\n        <div className={styles.modal} onClick={() => setSelectedImage(null)}>\r\n          <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>\r\n            <button \r\n              className={styles.closeBtn}\r\n              onClick={() => setSelectedImage(null)}\r\n            >\r\n              ×\r\n            </button>\r\n            <Image\r\n              src={selectedImage.image}\r\n              alt={selectedImage.title}\r\n              width={800}\r\n              height={600}\r\n              className={styles.modalImage}\r\n            />\r\n            <div className={styles.modalInfo}>\r\n              <h2>{selectedImage.title}</h2>\r\n              <p>{selectedImage.description}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* CTA Section */}\r\n      <section className={styles.ctaSection}>\r\n        <div className={styles.ctaContent}>\r\n          <h2>Ready to Start Your Project?</h2>\r\n          <p>Let our master craftsmen bring your vision to life with custom cast stone creations.</p>\r\n          <button className={styles.ctaBtn}>Get Started Today</button>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className={styles.footer}>\r\n        <div className={styles.footerContent}>\r\n          <div className={styles.footerSection}>\r\n            <h3>Cast Stone</h3>\r\n            <p>Creating timeless beauty with handcrafted cast stone elements for over 25 years.</p>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Quick Links</h4>\r\n            <ul>\r\n              <li><a href=\"/products\">Products</a></li>\r\n              <li><a href=\"/gallery\">Gallery</a></li>\r\n              <li><a href=\"/about\">About Us</a></li>\r\n              <li><a href=\"/contact\">Contact</a></li>\r\n            </ul>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Contact Info</h4>\r\n            <p>123 Artisan Way<br />Craftsman City, CC 12345</p>\r\n            <p>Phone: (*************</p>\r\n            <p>Email: <EMAIL></p>\r\n          </div>\r\n        </div>\r\n        <div className={styles.footerBottom}>\r\n          <p>&copy; 2024 Cast Stone Interiors. All rights reserved.</p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;QAAe;QAClC;YAAE,IAAI;YAAe,MAAM;QAAc;QACzC;YAAE,IAAI;YAAc,MAAM;QAAa;QACvC;YAAE,IAAI;YAAU,MAAM;QAAmB;QACzC;YAAE,IAAI;YAAe,MAAM;QAAc;KAC1C;IAED,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,gBAAgB,qBAAqB,QACvC,eACA,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAElD,qBACE,8OAAC;QAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,8OAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;sCACjC,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,OAAO;;8CAC3B,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAI;;;;;;;;;;;8CAChB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAY;;;;;;;;;;;8CACxB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAW,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;kDAAE;;;;;;;;;;;8CACjD,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;8CACrB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;sCAChC,8OAAC;4BAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sCAAE;;;;;;sCACjC,8OAAC;4BAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;;;;;;;;;;;;0BAQvC,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;;kCACvC,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;kCAClC,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;gCAEC,WAAW,GAAG,yIAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAChC,qBAAqB,SAAS,EAAE,GAAG,yIAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IACnD;gCACF,SAAS,IAAM,oBAAoB,SAAS,EAAE;0CAE7C,SAAS,IAAI;+BANT,SAAS,EAAE;;;;;;;;;;kCAYtB,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;kCAC/B,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC;gCAEC,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;gCAC7B,SAAS,IAAM,iBAAiB;0CAEhC,cAAA,8OAAC;oCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,KAAK;4CACf,KAAK,KAAK,KAAK;4CACf,OAAO;4CACP,QAAQ;4CACR,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;;;;;sDAEhC,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;8DACjC,8OAAC;oDAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;8DAAG,KAAK,KAAK;;;;;;8DAC7C,8OAAC;oDAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,gBAAgB;8DAAG,KAAK,WAAW;;;;;;8DACxD,8OAAC;oDAAO,WAAW,yIAAA,CAAA,UAAM,CAAC,OAAO;8DAAE;;;;;;;;;;;;;;;;;;+BAflC,KAAK,EAAE;;;;;;;;;;;;;;;;YAwBnB,+BACC,8OAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;gBAAE,SAAS,IAAM,iBAAiB;0BAC5D,cAAA,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;oBAAE,SAAS,CAAC,IAAM,EAAE,eAAe;;sCACpE,8OAAC;4BACC,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;4BAC1B,SAAS,IAAM,iBAAiB;sCACjC;;;;;;sCAGD,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,cAAc,KAAK;4BACxB,KAAK,cAAc,KAAK;4BACxB,OAAO;4BACP,QAAQ;4BACR,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;;;;;;sCAE9B,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;8CAAI,cAAc,KAAK;;;;;;8CACxB,8OAAC;8CAAG,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;;sCAC/B,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAO,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;sCAAE;;;;;;;;;;;;;;;;;0BAKtC,8OAAC;gBAAO,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;;kCAC9B,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;;0DACC,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAY;;;;;;;;;;;0DACxB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;0DACvB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAS;;;;;;;;;;;0DACrB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;0CAG3B,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;;4CAAE;0DAAe,8OAAC;;;;;4CAAK;;;;;;;kDACxB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAGP,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;kCACjC,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}