{"name": "lightningcss-win32-x64-msvc", "version": "1.24.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.win32-x64-msvc.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "files": ["lightningcss.win32-x64-msvc.node"], "resolutions": {"lightningcss": "link:."}, "os": ["win32"], "cpu": ["x64"]}