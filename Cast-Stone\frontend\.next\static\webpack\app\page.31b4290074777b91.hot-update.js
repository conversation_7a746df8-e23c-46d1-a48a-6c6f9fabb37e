"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const [currentCategory, setCurrentCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentProduct, setCurrentProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentTestimonial, setCurrentTestimonial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const categories = [\n        {\n            id: 1,\n            title: \"Architectural Designs\",\n            description: \"Professional architectural cast stone elements\",\n            image: \"/images/architectural-collection.svg\"\n        },\n        {\n            id: 2,\n            title: \"Designer Products\",\n            description: \"Luxury designer cast stone pieces\",\n            image: \"/images/fireplace-collection.svg\"\n        },\n        {\n            id: 3,\n            title: \"Limited Edition Products\",\n            description: \"Exclusive limited edition collections\",\n            image: \"/images/garden-collection.svg\"\n        },\n        {\n            id: 4,\n            title: \"Sealer Maintenance Program\",\n            description: \"Professional maintenance and sealing services\",\n            image: \"/images/hero-cast-stone.svg\"\n        }\n    ];\n    const featuredProducts = [\n        {\n            id: 1,\n            name: \"Classic Fireplace Mantel\",\n            price: \"$2,500\",\n            image: \"/images/fireplace-collection.svg\",\n            description: \"Handcrafted traditional mantel\"\n        },\n        {\n            id: 2,\n            name: \"Garden Fountain\",\n            price: \"$1,800\",\n            image: \"/images/garden-collection.svg\",\n            description: \"Three-tier fountain\"\n        },\n        {\n            id: 3,\n            name: \"Classical Columns\",\n            price: \"$1,200\",\n            image: \"/images/architectural-collection.svg\",\n            description: \"Corinthian style columns\"\n        }\n    ];\n    const testimonials = [\n        {\n            id: 1,\n            name: \"Sarah Johnson\",\n            company: \"Johnson Architecture\",\n            text: \"Cast Stone's architectural elements transformed our project. The quality and craftsmanship are unmatched.\",\n            rating: 5\n        },\n        {\n            id: 2,\n            name: \"Michael Chen\",\n            company: \"Elite Homes\",\n            text: \"We've been using Cast Stone products for over 10 years. Their consistency and beauty never disappoint.\",\n            rating: 5\n        },\n        {\n            id: 3,\n            name: \"Emma Rodriguez\",\n            company: \"Rodriguez Design Studio\",\n            text: \"The limited edition pieces add such elegance to our high-end residential projects.\",\n            rating: 5\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const categoryInterval = setInterval({\n                \"Home.useEffect.categoryInterval\": ()=>{\n                    setCurrentCategory({\n                        \"Home.useEffect.categoryInterval\": (prev)=>(prev + 1) % categories.length\n                    }[\"Home.useEffect.categoryInterval\"]);\n                }\n            }[\"Home.useEffect.categoryInterval\"], 4000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(categoryInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        categories.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const productInterval = setInterval({\n                \"Home.useEffect.productInterval\": ()=>{\n                    setCurrentProduct({\n                        \"Home.useEffect.productInterval\": (prev)=>(prev + 1) % featuredProducts.length\n                    }[\"Home.useEffect.productInterval\"]);\n                }\n            }[\"Home.useEffect.productInterval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(productInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        featuredProducts.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const testimonialInterval = setInterval({\n                \"Home.useEffect.testimonialInterval\": ()=>{\n                    setCurrentTestimonial({\n                        \"Home.useEffect.testimonialInterval\": (prev)=>(prev + 1) % testimonials.length\n                    }[\"Home.useEffect.testimonialInterval\"]);\n                }\n            }[\"Home.useEffect.testimonialInterval\"], 6000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(testimonialInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        testimonials.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().navigation),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().navContainer),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    children: \"Cast Stone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Interiors & Decorations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().navMenu),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/contact\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/about\",\n                                                        children: \"Our Story\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/retail-locator\",\n                                                        children: \"Retail Locator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/wholesale-signup\",\n                                                        children: \"Wholesale Sign-up\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/architectural\",\n                                                        children: \"Architectural Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/designer\",\n                                                        children: \"Designer Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/limited-edition\",\n                                                        children: \"Limited Edition Designs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/sealers\",\n                                                        children: \"Cast Stone Sealers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/collections\",\n                                        children: \"Collections\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/projects\",\n                                        children: \"Completed Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/catalog\",\n                                                        children: \"Catalog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/finishes\",\n                                                        children: \"Finishes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/videos\",\n                                                        children: \"Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/technical\",\n                                                        children: \"Technical Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/faqs\",\n                                                        children: \"FAQs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().hero),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().videoBackground),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                autoPlay: true,\n                                muted: true,\n                                loop: true,\n                                playsInline: true,\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroVideo),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: \"/herosection.mp4\",\n                                        type: \"video/mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Your browser does not support the video tag.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().videoOverlay)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroContent),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTitle),\n                                children: [\n                                    \"Timeless Elegance in\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().highlight),\n                                        children: \" Cast Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSubtitle),\n                                children: \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroActions),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryBtn),\n                                        children: \"Explore Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryBtn),\n                                        children: \"Watch Our Story\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoriesSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().sectionHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Our Collections\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Explore our diverse range of cast stone products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoriesCarousel),\n                        children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categorySlide), \" \").concat(index === currentCategory ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryCard),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryImage),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: category.image,\n                                                alt: category.title,\n                                                width: 400,\n                                                height: 300,\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryImg)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryInfo),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: category.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: category.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryBtn),\n                                                    children: \"Explore\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryControls),\n                        children: categories.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryDot), \" \").concat(index === currentCategory ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().activeDot) : ''),\n                                onClick: ()=>setCurrentCategory(index)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogContainer),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogContent),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogText),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Explore Our Complete Catalog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Browse through our comprehensive collection of cast stone products. From architectural elements to decorative pieces, find everything you need to transform your space with timeless elegance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogBtn),\n                                        children: \"View Catalog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/images/catalog-preview.jpg\",\n                                alt: \"Cast Stone Catalog\",\n                                width: 600,\n                                height: 400,\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().featuredProducts),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().sectionHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"This Week's Featured Products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Handpicked selections showcasing our finest craftsmanship\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCarousel),\n                        children: featuredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productSlide), \" \").concat(index === currentProduct ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCard),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImageContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: product.image,\n                                                    alt: product.name,\n                                                    width: 400,\n                                                    height: 300,\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImage)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productOverlay),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickViewBtn),\n                                                        children: \"Quick View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: product.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productFooter),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productPrice),\n                                                            children: product.price\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().inquireBtn),\n                                                            children: \"Inquire\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            }, product.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productControls),\n                        children: featuredProducts.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productDot), \" \").concat(index === currentProduct ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().activeDot) : ''),\n                                onClick: ()=>setCurrentProduct(index)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialsSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().sectionHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"What Our Clients Say\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Hear from professionals who trust Cast Stone for their projects\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialsCarousel),\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialSlide), \" \").concat(index === currentTestimonial ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialCard),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().stars),\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().star),\n                                                        children: \"★\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialText),\n                                                children: [\n                                                    '\"',\n                                                    testimonial.text,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialAuthor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: testimonial.company\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this)\n                            }, testimonial.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialControls),\n                        children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialDot), \" \").concat(index === currentTestimonial ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().activeDot) : ''),\n                                onClick: ()=>setCurrentTestimonial(index)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerContent),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Cast Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Creating timeless beauty with handcrafted cast stone elements for over 25 years.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/contact\",\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/about\",\n                                                    children: \"Our Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/retail-locator\",\n                                                    children: \"Retail Locator\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/wholesale-signup\",\n                                                    children: \"Wholesale Sign-up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/architectural\",\n                                                    children: \"Architectural Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/designer\",\n                                                    children: \"Designer Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/limited-edition\",\n                                                    children: \"Limited Edition\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/sealers\",\n                                                    children: \"Cast Stone Sealers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Discover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/catalog\",\n                                                    children: \"Catalog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections\",\n                                                    children: \"Collections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/projects\",\n                                                    children: \"Completed Projects\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/videos\",\n                                                    children: \"Videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/faqs\",\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Contact Info\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"123 Artisan Way\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 31\n                                            }, this),\n                                            \"Craftsman City, CC 12345\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Phone: (*************\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Email: <EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerBottom),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Cast Stone Interiors. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"zMjsSn4HrDFMzD+viBxakPhvDxI=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});