{"name": "cast-stone", "version": "1.0.0", "description": "<PERSON> Stone - Full Stack Web Application with Node.js backend and Next.js frontend", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "rimraf backend/node_modules frontend/node_modules node_modules", "lint": "cd frontend && npm run lint", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "keywords": ["fullstack", "nodejs", "nextjs", "typescript", "mongodb", "express", "react"], "author": "", "license": "ISC", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}