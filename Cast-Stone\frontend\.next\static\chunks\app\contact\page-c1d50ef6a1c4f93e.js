(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{7016:(e,s,a)=>{Promise.resolve().then(a.bind(a,8983))},8231:e=>{e.exports={container:"page_container__5YXRc",navigation:"page_navigation__enjuy",navContainer:"page_navContainer__juTch",logo:"page_logo__PF8Tu",navMenu:"page_navMenu__fs6c0",active:"page_active__l9k2n",hero:"page_hero__0Vvk_",heroContent:"page_heroContent__EA5kU",heroTitle:"page_heroTitle__m_yzc",heroSubtitle:"page_heroSubtitle__AdiMC",contactSection:"page_contactSection__AuYnq",contactContainer:"page_contactContainer___69nj",formContainer:"page_formContainer__UE5_i",form:"page_form__arM1T",formRow:"page_formRow__qjAzl",formGroup:"page_formGroup__bIAM3",label:"page_label__zFMfb",input:"page_input__4_GuJ",textarea:"page_textarea__ppW_b",select:"page_select__zrwMk",submitButton:"page_submitButton__jMO2u",contactInfo:"page_contactInfo__jt5gr",infoGrid:"page_infoGrid__wjy0H",infoItem:"page_infoItem__dqhjP",infoIcon:"page_infoIcon__zNB2y",infoTitle:"page_infoTitle__OG24k",infoText:"page_infoText__WCFyB",infoSubtext:"page_infoSubtext__nfiIm",footer:"page_footer__wSOJ4",footerContent:"page_footerContent__bl1zS",footerSection:"page_footerSection__dqQQL",footerBottom:"page_footerBottom__aYKxl"}},8983:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});var n=a(5155),i=a(2115),l=a(8231),o=a.n(l);function r(){let[e,s]=(0,i.useState)({name:"",email:"",phone:"",projectType:"",message:""}),a=a=>{s({...e,[a.target.name]:a.target.value})};return(0,n.jsxs)("div",{className:o().container,children:[(0,n.jsx)("nav",{className:o().navigation,children:(0,n.jsxs)("div",{className:o().navContainer,children:[(0,n.jsxs)("div",{className:o().logo,children:[(0,n.jsx)("h1",{children:"Cast Stone"}),(0,n.jsx)("span",{children:"Interiors & Decorations"})]}),(0,n.jsxs)("ul",{className:o().navMenu,children:[(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/",children:"Home"})}),(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/products",children:"Products"})}),(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/gallery",children:"Gallery"})}),(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/about",children:"About"})}),(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/contact",className:o().active,children:"Contact"})})]})]})}),(0,n.jsx)("section",{className:o().hero,children:(0,n.jsxs)("div",{className:o().heroContent,children:[(0,n.jsx)("h1",{className:o().heroTitle,children:"Contact Us"}),(0,n.jsx)("p",{className:o().heroSubtitle,children:"Ready to transform your space? Get in touch with our team of experts to discuss your cast stone project."})]})}),(0,n.jsx)("section",{className:o().contactSection,children:(0,n.jsxs)("div",{className:o().contactContainer,children:[(0,n.jsxs)("div",{className:o().formContainer,children:[(0,n.jsx)("h2",{children:"Start Your Project"}),(0,n.jsx)("p",{children:"Fill out the form below and we'll get back to you within 24 hours."}),(0,n.jsxs)("form",{className:o().form,onSubmit:s=>{s.preventDefault(),console.log("Form submitted:",e)},children:[(0,n.jsxs)("div",{className:o().formRow,children:[(0,n.jsxs)("div",{className:o().formGroup,children:[(0,n.jsx)("label",{htmlFor:"name",className:o().label,children:"Full Name *"}),(0,n.jsx)("input",{type:"text",id:"name",name:"name",className:o().input,placeholder:"Your full name",value:e.name,onChange:a,required:!0})]}),(0,n.jsxs)("div",{className:o().formGroup,children:[(0,n.jsx)("label",{htmlFor:"email",className:o().label,children:"Email Address *"}),(0,n.jsx)("input",{type:"email",id:"email",name:"email",className:o().input,placeholder:"<EMAIL>",value:e.email,onChange:a,required:!0})]})]}),(0,n.jsxs)("div",{className:o().formRow,children:[(0,n.jsxs)("div",{className:o().formGroup,children:[(0,n.jsx)("label",{htmlFor:"phone",className:o().label,children:"Phone Number"}),(0,n.jsx)("input",{type:"tel",id:"phone",name:"phone",className:o().input,placeholder:"(*************",value:e.phone,onChange:a})]}),(0,n.jsxs)("div",{className:o().formGroup,children:[(0,n.jsx)("label",{htmlFor:"projectType",className:o().label,children:"Project Type"}),(0,n.jsxs)("select",{id:"projectType",name:"projectType",className:o().select,value:e.projectType,onChange:a,children:[(0,n.jsx)("option",{value:"",children:"Select project type"}),(0,n.jsx)("option",{value:"fireplace",children:"Fireplace"}),(0,n.jsx)("option",{value:"garden",children:"Garden Features"}),(0,n.jsx)("option",{value:"architectural",children:"Architectural Elements"}),(0,n.jsx)("option",{value:"restoration",children:"Restoration"}),(0,n.jsx)("option",{value:"custom",children:"Custom Design"})]})]})]}),(0,n.jsxs)("div",{className:o().formGroup,children:[(0,n.jsx)("label",{htmlFor:"message",className:o().label,children:"Project Details *"}),(0,n.jsx)("textarea",{id:"message",name:"message",rows:6,className:o().textarea,placeholder:"Tell us about your project, timeline, and any specific requirements...",value:e.message,onChange:a,required:!0})]}),(0,n.jsx)("button",{type:"submit",className:o().submitButton,children:"Send Message"})]})]}),(0,n.jsxs)("div",{className:o().contactInfo,children:[(0,n.jsx)("h2",{children:"Get In Touch"}),(0,n.jsx)("p",{children:"We're here to help bring your vision to life."}),(0,n.jsxs)("div",{className:o().infoGrid,children:[(0,n.jsxs)("div",{className:o().infoItem,children:[(0,n.jsx)("div",{className:o().infoIcon,children:"\uD83D\uDCCD"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:o().infoTitle,children:"Visit Our Workshop"}),(0,n.jsxs)("p",{className:o().infoText,children:["123 Artisan Way",(0,n.jsx)("br",{}),"Craftsman City, CC 12345"]})]})]}),(0,n.jsxs)("div",{className:o().infoItem,children:[(0,n.jsx)("div",{className:o().infoIcon,children:"\uD83D\uDCDE"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:o().infoTitle,children:"Call Us"}),(0,n.jsx)("p",{className:o().infoText,children:"(*************"}),(0,n.jsx)("p",{className:o().infoSubtext,children:"Mon-Fri 8AM-6PM"})]})]}),(0,n.jsxs)("div",{className:o().infoItem,children:[(0,n.jsx)("div",{className:o().infoIcon,children:"✉️"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:o().infoTitle,children:"Email Us"}),(0,n.jsx)("p",{className:o().infoText,children:"<EMAIL>"}),(0,n.jsx)("p",{className:o().infoSubtext,children:"We respond within 24 hours"})]})]}),(0,n.jsxs)("div",{className:o().infoItem,children:[(0,n.jsx)("div",{className:o().infoIcon,children:"\uD83D\uDD52"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:o().infoTitle,children:"Business Hours"}),(0,n.jsxs)("p",{className:o().infoText,children:["Monday - Friday: 8:00 AM - 6:00 PM",(0,n.jsx)("br",{}),"Saturday: 9:00 AM - 4:00 PM",(0,n.jsx)("br",{}),"Sunday: Closed"]})]})]})]})]})]})}),(0,n.jsxs)("footer",{className:o().footer,children:[(0,n.jsxs)("div",{className:o().footerContent,children:[(0,n.jsxs)("div",{className:o().footerSection,children:[(0,n.jsx)("h3",{children:"Cast Stone"}),(0,n.jsx)("p",{children:"Creating timeless beauty with handcrafted cast stone elements for over 25 years."})]}),(0,n.jsxs)("div",{className:o().footerSection,children:[(0,n.jsx)("h4",{children:"Quick Links"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/products",children:"Products"})}),(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/gallery",children:"Gallery"})}),(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/about",children:"About Us"})}),(0,n.jsx)("li",{children:(0,n.jsx)("a",{href:"/contact",children:"Contact"})})]})]}),(0,n.jsxs)("div",{className:o().footerSection,children:[(0,n.jsx)("h4",{children:"Contact Info"}),(0,n.jsxs)("p",{children:["123 Artisan Way",(0,n.jsx)("br",{}),"Craftsman City, CC 12345"]}),(0,n.jsx)("p",{children:"Phone: (*************"}),(0,n.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,n.jsx)("div",{className:o().footerBottom,children:(0,n.jsx)("p",{children:"\xa9 2024 Cast Stone Interiors. All rights reserved."})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[480,441,684,358],()=>s(7016)),_N_E=e.O()}]);