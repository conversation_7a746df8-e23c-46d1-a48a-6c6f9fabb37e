{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/contact/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"backLink\": \"page-module__OSLHOG__backLink\",\n  \"contactInfo\": \"page-module__OSLHOG__contactInfo\",\n  \"container\": \"page-module__OSLHOG__container\",\n  \"description\": \"page-module__OSLHOG__description\",\n  \"form\": \"page-module__OSLHOG__form\",\n  \"formGroup\": \"page-module__OSLHOG__formGroup\",\n  \"infoItem\": \"page-module__OSLHOG__infoItem\",\n  \"infoText\": \"page-module__OSLHOG__infoText\",\n  \"infoTitle\": \"page-module__OSLHOG__infoTitle\",\n  \"input\": \"page-module__OSLHOG__input\",\n  \"label\": \"page-module__OSLHOG__label\",\n  \"main\": \"page-module__OSLHOG__main\",\n  \"submitButton\": \"page-module__OSLHOG__submitButton\",\n  \"textarea\": \"page-module__OSLHOG__textarea\",\n  \"title\": \"page-module__OSLHOG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/contact/page.tsx"], "sourcesContent": ["import styles from \"./page.module.css\";\n\nexport default function Contact() {\n  return (\n    <div className={styles.container}>\n      <main className={styles.main}>\n        <h1 className={styles.title}>Contact Us</h1>\n        <p className={styles.description}>\n          Get in touch with us. We'd love to hear from you!\n        </p>\n        \n        <form className={styles.form}>\n          <div className={styles.formGroup}>\n            <label htmlFor=\"name\" className={styles.label}>\n              Name\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              className={styles.input}\n              placeholder=\"Your name\"\n              required\n            />\n          </div>\n          \n          <div className={styles.formGroup}>\n            <label htmlFor=\"email\" className={styles.label}>\n              Email\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              className={styles.input}\n              placeholder=\"<EMAIL>\"\n              required\n            />\n          </div>\n          \n          <div className={styles.formGroup}>\n            <label htmlFor=\"message\" className={styles.label}>\n              Message\n            </label>\n            <textarea\n              id=\"message\"\n              name=\"message\"\n              rows={5}\n              className={styles.textarea}\n              placeholder=\"Your message here...\"\n              required\n            />\n          </div>\n          \n          <button type=\"submit\" className={styles.submitButton}>\n            Send Message\n          </button>\n        </form>\n        \n        <div className={styles.contactInfo}>\n          <div className={styles.infoItem}>\n            <h3 className={styles.infoTitle}>Email</h3>\n            <p className={styles.infoText}><EMAIL></p>\n          </div>\n          <div className={styles.infoItem}>\n            <h3 className={styles.infoTitle}>Phone</h3>\n            <p className={styles.infoText}>+****************</p>\n          </div>\n        </div>\n        \n        <a href=\"/\" className={styles.backLink}>\n          ← Back to Home\n        </a>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;kBAC9B,cAAA,8OAAC;YAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;;8BAC1B,8OAAC;oBAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;8BAAE;;;;;;8BAC7B,8OAAC;oBAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;8BAAE;;;;;;8BAIlC,8OAAC;oBAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;;sCAC1B,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAM,SAAQ;oCAAO,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;8CAAE;;;;;;8CAG/C,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;oCACvB,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAM,SAAQ;oCAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;8CAAE;;;;;;8CAGhD,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;oCACvB,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAM,SAAQ;oCAAU,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;8CAAE;;;;;;8CAGlD,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAM;oCACN,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;oCAC1B,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;4BAAO,MAAK;4BAAS,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;;;;;;;8BAKxD,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;sCAChC,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;8CAC7B,8OAAC;oCAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;8CACjC,8OAAC;oCAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;8CAAE;;;;;;;;;;;;sCAEjC,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;8CAC7B,8OAAC;oCAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;8CACjC,8OAAC;oCAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;8CAAE;;;;;;;;;;;;;;;;;;8BAInC,8OAAC;oBAAE,MAAK;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;8BAAE;;;;;;;;;;;;;;;;;AAMhD", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,IAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}