/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/page.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* Cast Stone Homepage Styles */

.page_container__aoG4z {
  min-height: 100vh;
  background: var(--background-light);
  color: var(--text-dark);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
}

/* Top Navigation Bar */
.page_navigation__sIIfG {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.page_navContainer__DkD_r {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page_logo__7fc9l h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  letter-spacing: -0.02em;
}

.page_logo__7fc9l span {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.page_navMenu__1H8jk {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
  align-items: center;
}

.page_navMenu__1H8jk > li {
  position: relative;
}

.page_navMenu__1H8jk a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
  padding: 0.5rem 0;
}

.page_navMenu__1H8jk a:hover {
  color: var(--primary-color);
}

/* Dropdown Styles */
.page_dropdown__fSg_R {
  position: relative;
}

.page_dropdownToggle__DQIfV::after {
  content: '▼';
  font-size: 0.7rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.page_dropdown__fSg_R:hover .page_dropdownToggle__DQIfV::after {
  transform: rotate(180deg);
}

.page_dropdownMenu__qkuLY {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 200px;
  box-shadow: var(--shadow);
  border-radius: 8px;
  padding: 0.5rem 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
}

.page_dropdown__fSg_R:hover .page_dropdownMenu__qkuLY {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.page_dropdownMenu__qkuLY li {
  margin: 0;
}

.page_dropdownMenu__qkuLY a {
  display: block;
  padding: 0.75rem 1.5rem;
  color: var(--text-dark);
  text-decoration: none;
  transition: background-color 0.3s ease;
  font-weight: 400;
}

.page_dropdownMenu__qkuLY a:hover {
  background-color: var(--background-light);
  color: var(--primary-color);
}

/* Hero Section with Video Background */
.page_hero__SKW6o {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.page_videoBackground__U08pI {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.page_heroVideo__tANmU {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.page_videoOverlay__lcJrw {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.page_heroContent__2lPR8 {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.page_heroTitle__Gfler {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.page_highlight__KaGfy {
  color: var(--secondary-color);
}

.page_heroSubtitle__RTAw0 {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.page_heroActions__8_tzb {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.page_primaryBtn__smNNv {
  padding: 1rem 2.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_primaryBtn__smNNv:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-hover);
}

.page_secondaryBtn__Re3F8 {
  padding: 1rem 2.5rem;
  background: transparent;
  color: white;
  border: 2px solid white;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_secondaryBtn__Re3F8:hover {
  background: white;
  color: var(--primary-color);
  transform: translateY(-3px);
}

/* Section Headers */
.page_sectionHeader__a4Fw5 {
  text-align: center;
  margin-bottom: 3rem;
}

.page_sectionHeader__a4Fw5 h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.page_sectionHeader__a4Fw5 p {
  font-size: 1.1rem;
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto;
}

/* Categories Carousel */
.page_categoriesSection__iAE4o {
  padding: 6rem 2rem;
  background: white;
}

.page_categoriesCarousel__RFyWb {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 15px;
}

.page_categorySlide__CdSEy {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.page_categorySlide__CdSEy.page_active__q3_T3 {
  display: block;
  opacity: 1;
}

.page_categoryCard__8vOUo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  padding: 3rem;
  background: var(--background-light);
  border-radius: 15px;
}

.page_categoryImage__iTTGG {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.page_categoryImg__oim5y {
  width: 100%;
  height: 300px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.page_categoryCard__8vOUo:hover .page_categoryImg__oim5y {
  transform: scale(1.05);
}

.page_categoryInfo__TLbyz {
  padding: 2rem;
}

.page_categoryInfo__TLbyz h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.page_categoryInfo__TLbyz p {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.page_categoryBtn__b_Azx {
  padding: 0.75rem 2rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_categoryBtn__b_Azx:hover {
  background: var(--accent-color);
  transform: translateY(-2px);
}

.page_categoryControls__ytbu2 {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.page_categoryDot__ZcVUr {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_categoryDot__ZcVUr.page_activeDot__uXfHl {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Online Catalog Section */
.page_catalogSection__IzVtz {
  padding: 6rem 2rem;
  background: var(--primary-color);
  color: white;
}

.page_catalogContainer__sfHpk {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.page_catalogContent__l2Bea {
  padding: 2rem;
}

.page_catalogText__BYpJP h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: white;
}

.page_catalogText__BYpJP p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.page_catalogBtn__vnlcy {
  padding: 1rem 2.5rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_catalogBtn__vnlcy:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
}

.page_catalogImage__XmRth {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
}

.page_catalogImg__bS2F_ {
  width: 100%;
  height: 400px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.page_catalogContainer__sfHpk:hover .page_catalogImg__bS2F_ {
  transform: scale(1.05);
}

/* Weekly Featured Products */
.page_featuredProducts__0nTqm {
  padding: 6rem 2rem;
  background: white;
}

.page_productCarousel__s6l3G {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
}

.page_productSlide__DytJU {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.page_productSlide__DytJU.page_active__q3_T3 {
  display: block;
  opacity: 1;
}

.page_productCard__LtAQV {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  max-width: 500px;
  margin: 0 auto;
}

.page_productCard__LtAQV:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
}

.page_productImageContainer__uJZEz {
  position: relative;
  overflow: hidden;
}

.page_productImage__ApZwb {
  width: 100%;
  height: 300px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.page_productCard__LtAQV:hover .page_productImage__ApZwb {
  transform: scale(1.05);
}

.page_productOverlay__eKPus {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.page_productCard__LtAQV:hover .page_productOverlay__eKPus {
  opacity: 1;
}

.page_quickViewBtn__xurCW {
  padding: 0.75rem 1.5rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_quickViewBtn__xurCW:hover {
  transform: translateY(-2px);
}

.page_productInfo__6QZeh {
  padding: 2rem;
}

.page_productInfo__6QZeh h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.page_productInfo__6QZeh p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.page_productFooter__mOgmf {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page_productPrice__9UDVu {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--primary-color);
}

.page_inquireBtn__N2NGi {
  padding: 0.5rem 1.5rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_inquireBtn__N2NGi:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}

.page_productControls__HioX6 {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.page_productDot__QdKmu {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_productDot__QdKmu.page_activeDot__uXfHl {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Testimonials Section */
.page_testimonialsSection__exkDv {
  padding: 6rem 2rem;
  background: var(--background-light);
}

.page_testimonialsCarousel__OeiYQ {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

.page_testimonialSlide__dH9_D {
  display: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.page_testimonialSlide__dH9_D.page_active__q3_T3 {
  display: block;
  opacity: 1;
}

.page_testimonialCard__8YWAu {
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow);
  text-align: center;
}

.page_stars__IfV3K {
  margin-bottom: 1.5rem;
}

.page_star__v4NEN {
  color: #ffd700;
  font-size: 1.5rem;
  margin: 0 0.1rem;
}

.page_testimonialText__hLDzx {
  font-size: 1.2rem;
  font-style: italic;
  color: var(--text-dark);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.page_testimonialAuthor__zFcbB h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.page_testimonialAuthor__zFcbB span {
  color: var(--text-light);
  font-size: 0.9rem;
}

.page_testimonialControls__Ce712 {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.page_testimonialDot__5tyJ2 {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_testimonialDot__5tyJ2.page_activeDot__uXfHl {
  background: var(--primary-color);
  transform: scale(1.2);
}

/* Footer */
.page_footer__sHKi3 {
  background: var(--text-dark);
  color: white;
  padding: 4rem 2rem 2rem;
}

.page_footerContent__sUmFz {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 2rem;
}

.page_footerSection__t2TqJ h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.page_footerSection__t2TqJ h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.page_footerSection__t2TqJ p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.page_footerSection__t2TqJ ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.page_footerSection__t2TqJ ul li {
  margin-bottom: 0.5rem;
}

.page_footerSection__t2TqJ ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.page_footerSection__t2TqJ ul li a:hover {
  color: var(--secondary-color);
}

.page_footerBottom__cWZ_v {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Mobile Hamburger Menu */
.page_mobileMenuToggle__vGVDJ {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.page_hamburgerLine__s6yqT {
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.page_mobileMenuToggle__vGVDJ.page_active__q3_T3 .page_hamburgerLine__s6yqT:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.page_mobileMenuToggle__vGVDJ.page_active__q3_T3 .page_hamburgerLine__s6yqT:nth-child(2) {
  opacity: 0;
}

.page_mobileMenuToggle__vGVDJ.page_active__q3_T3 .page_hamburgerLine__s6yqT:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.page_mobileMenu__RbNgw {
  position: fixed;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100vh;
  background: white;
  z-index: 1000;
  transition: left 0.3s ease;
  padding-top: 80px;
  overflow-y: auto;
}

.page_mobileMenu__RbNgw.page_active__q3_T3 {
  left: 0;
}

.page_mobileNavMenu__cmEee {
  list-style: none;
  padding: 2rem;
  margin: 0;
}

.page_mobileNavMenu__cmEee > li {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--background-light);
  padding-bottom: 1rem;
}

.page_mobileNavMenu__cmEee a {
  display: block;
  padding: 1rem 0;
  color: var(--text-dark);
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;
}

.page_mobileDropdownToggle__45djA {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.page_mobileDropdownToggle__45djA::after {
  content: '+';
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.page_mobileDropdownToggle__45djA.page_active__q3_T3::after {
  transform: rotate(45deg);
}

.page_mobileDropdownMenu__ZQpKd {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--background-light);
  margin-top: 1rem;
  border-radius: 8px;
}

.page_mobileDropdownMenu__ZQpKd.page_active__q3_T3 {
  max-height: 300px;
}

.page_mobileDropdownMenu__ZQpKd li {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.page_mobileDropdownMenu__ZQpKd a {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Navigation */
  .page_navMenu__1H8jk {
    display: none;
  }

  .page_mobileMenuToggle__vGVDJ {
    display: flex;
  }

  .page_navContainer__DkD_r {
    padding: 0 1rem;
  }

  /* Hero Section */
  .page_heroTitle__Gfler {
    font-size: 2.5rem;
  }

  .page_heroSubtitle__RTAw0 {
    font-size: 1.1rem;
  }

  .page_heroActions__8_tzb {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .page_primaryBtn__smNNv,
  .page_secondaryBtn__Re3F8 {
    width: 100%;
    max-width: 280px;
  }

  /* Categories */
  .page_categoryCard__8vOUo {
    grid-template-columns: 1fr;
    text-align: center;
    padding: 2rem;
  }

  .page_categoryImage__iTTGG {
    order: -1;
  }

  /* Catalog */
  .page_catalogContainer__sfHpk {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .page_catalogContent__l2Bea {
    padding: 1rem;
  }

  /* Section Headers */
  .page_sectionHeader__a4Fw5 h2 {
    font-size: 2rem;
  }

  .page_sectionHeader__a4Fw5 {
    margin-bottom: 2rem;
  }

  /* Testimonials */
  .page_testimonialCard__8YWAu {
    padding: 2rem;
  }

  .page_testimonialText__hLDzx {
    font-size: 1.1rem;
  }

  /* Footer */
  .page_footerContent__sUmFz {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .page_heroTitle__Gfler {
    font-size: 2rem;
  }

  .page_heroContent__2lPR8 {
    padding: 0 1rem;
  }

  .page_primaryBtn__smNNv,
  .page_secondaryBtn__Re3F8 {
    padding: 0.75rem 2rem;
    font-size: 1rem;
  }

  .page_sectionHeader__a4Fw5 h2 {
    font-size: 1.8rem;
  }

  .page_categoryInfo__TLbyz,
  .page_testimonialCard__8YWAu {
    padding: 1.5rem;
  }
}

.page_heroContent__2lPR8 {
  max-width: 500px;
}

.page_heroTitle__Gfler {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--text-dark);
}

.page_highlight__KaGfy {
  color: var(--primary-color);
  position: relative;
}

.page_highlight__KaGfy::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.page_heroSubtitle__RTAw0 {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 2.5rem;
  line-height: 1.7;
}

.page_heroActions__8_tzb {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.page_primaryBtn__smNNv {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
}

.page_primaryBtn__smNNv:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.page_secondaryBtn__Re3F8 {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_secondaryBtn__Re3F8:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.page_heroImage__Q6NCQ {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  box-shadow: var(--shadow);
}

.page_heroImg__Q4Nxm {
  width: 100%;
  height: auto;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.page_heroImage__Q6NCQ:hover .page_heroImg__Q4Nxm {
  transform: scale(1.05);
}

/* Section Headers */
.page_sectionHeader__a4Fw5 {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.page_sectionHeader__a4Fw5 h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.page_sectionHeader__a4Fw5 p {
  font-size: 1.1rem;
  color: var(--text-light);
}

/* Video Carousel Section */
.page_videoSection__OHo_Q {
  padding: 6rem 2rem;
  background: white;
  overflow: hidden;
}

.page_videoCarousel__YWCit {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page_videoSlide__SQVfa {
  position: absolute;
  width: 100%;
  opacity: 0;
  transform: translateX(100px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.page_videoSlide__SQVfa.page_active__q3_T3 {
  opacity: 1;
  transform: translateX(0);
  pointer-events: all;
}

.page_slideFromLeft__6d8kZ {
  transform: translateX(-100px);
}

.page_slideFromLeft__6d8kZ.page_active__q3_T3 {
  transform: translateX(0);
}

.page_slideFromRight__mxQie {
  transform: translateX(100px);
}

.page_slideFromRight__mxQie.page_active__q3_T3 {
  transform: translateX(0);
}

.page_videoContainer__dDrrq {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  max-width: 900px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page_videoContainer__dDrrq {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

.page_videoPlaceholder__TDeI7 {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.page_videoPlaceholder__TDeI7:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.page_videoThumbnail__rxPZz {
  width: 100%;
  height: 300px;
  -o-object-fit: cover;
     object-fit: cover;
}

.page_playButton__egZrD {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease;
}

.page_videoPlaceholder__TDeI7:hover .page_playButton__egZrD {
  transform: translate(-50%, -50%) scale(1.1);
}

.page_videoInfo__bv2xv h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.page_videoInfo__bv2xv p {
  font-size: 1.1rem;
  color: var(--text-light);
  line-height: 1.6;
}

.page_videoControls__rsGt_ {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 3rem;
}

.page_videoDot__v_JTG {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(139, 69, 19, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.page_videoDot__v_JTG.page_activeDot__uXfHl {
  background: var(--primary-color);
  transform: scale(1.2);
}

.page_videoDot__v_JTG:hover {
  background: var(--primary-color);
}

/* Featured Products */
.page_featuredProducts__0nTqm {
  padding: 6rem 2rem;
  background: var(--background-light);
}

.page_productGrid__GJuvN {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page_productCard__LtAQV {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page_productCard__LtAQV:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
}

.page_productImage__ApZwb {
  width: 100%;
  height: 250px;
  -o-object-fit: cover;
     object-fit: cover;
}

.page_productInfo__6QZeh {
  padding: 1.5rem;
}

.page_productInfo__6QZeh h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.page_productInfo__6QZeh p {
  color: var(--text-light);
  margin-bottom: 1rem;
}

.page_productPrice__9UDVu {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Newsletter */
.page_newsletter__86quH {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  text-align: center;
}

.page_newsletterContent__On6Jf h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.page_newsletterContent__On6Jf p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.page_newsletterForm__afwak {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
  gap: 1rem;
}

@media (max-width: 640px) {
  .page_newsletterForm__afwak {
    flex-direction: column;
  }
}

.page_emailInput__6WJmn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  outline: none;
}

.page_subscribeBtn__sZTMw {
  background: white;
  color: var(--primary-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.page_subscribeBtn__sZTMw:hover {
  background: var(--background-light);
  transform: translateY(-2px);
}

/* Footer */
.page_footer__sHKi3 {
  background: var(--text-dark);
  color: white;
  padding: 3rem 2rem 1rem;
}

.page_footerContent__sUmFz {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.page_footerSection__t2TqJ h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.page_footerSection__t2TqJ h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.page_footerSection__t2TqJ p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.page_footerSection__t2TqJ ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.page_footerSection__t2TqJ ul li {
  margin-bottom: 0.5rem;
}

.page_footerSection__t2TqJ ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.page_footerSection__t2TqJ ul li a:hover {
  color: var(--secondary-color);
}

.page_footerBottom__cWZ_v {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page_heroTitle__Gfler {
    font-size: 2.5rem;
  }

  .page_sectionHeader__a4Fw5 h2 {
    font-size: 2rem;
  }

  .page_navMenu__1H8jk {
    display: none;
  }

  .page_videoCarousel__YWCit {
    height: auto;
  }

  .page_videoSlide__SQVfa {
    position: relative;
    opacity: 1;
    transform: none;
  }

  .page_videoSlide__SQVfa.page_active__q3_T3 {
    display: block;
  }

  .page_videoSlide__SQVfa:not(.page_active__q3_T3) {
    display: none;
  }
}

