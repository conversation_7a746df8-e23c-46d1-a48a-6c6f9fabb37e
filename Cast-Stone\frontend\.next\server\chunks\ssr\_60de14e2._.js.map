{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/about/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"page-module__NfDiEG__active\",\n  \"container\": \"page-module__NfDiEG__container\",\n  \"ctaBtn\": \"page-module__NfDiEG__ctaBtn\",\n  \"ctaContent\": \"page-module__NfDiEG__ctaContent\",\n  \"ctaSection\": \"page-module__NfDiEG__ctaSection\",\n  \"footer\": \"page-module__NfDiEG__footer\",\n  \"footerBottom\": \"page-module__NfDiEG__footerBottom\",\n  \"footerContent\": \"page-module__NfDiEG__footerContent\",\n  \"footerSection\": \"page-module__NfDiEG__footerSection\",\n  \"hero\": \"page-module__NfDiEG__hero\",\n  \"heroContent\": \"page-module__NfDiEG__heroContent\",\n  \"heroSubtitle\": \"page-module__NfDiEG__heroSubtitle\",\n  \"heroTitle\": \"page-module__NfDiEG__heroTitle\",\n  \"logo\": \"page-module__NfDiEG__logo\",\n  \"navContainer\": \"page-module__NfDiEG__navContainer\",\n  \"navMenu\": \"page-module__NfDiEG__navMenu\",\n  \"navigation\": \"page-module__NfDiEG__navigation\",\n  \"sectionHeader\": \"page-module__NfDiEG__sectionHeader\",\n  \"storyContainer\": \"page-module__NfDiEG__storyContainer\",\n  \"storyContent\": \"page-module__NfDiEG__storyContent\",\n  \"storyImage\": \"page-module__NfDiEG__storyImage\",\n  \"storyImg\": \"page-module__NfDiEG__storyImg\",\n  \"storySection\": \"page-module__NfDiEG__storySection\",\n  \"teamGrid\": \"page-module__NfDiEG__teamGrid\",\n  \"teamMember\": \"page-module__NfDiEG__teamMember\",\n  \"teamPhoto\": \"page-module__NfDiEG__teamPhoto\",\n  \"teamRole\": \"page-module__NfDiEG__teamRole\",\n  \"teamSection\": \"page-module__NfDiEG__teamSection\",\n  \"valueCard\": \"page-module__NfDiEG__valueCard\",\n  \"valueIcon\": \"page-module__NfDiEG__valueIcon\",\n  \"valuesGrid\": \"page-module__NfDiEG__valuesGrid\",\n  \"valuesSection\": \"page-module__NfDiEG__valuesSection\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/about/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport styles from \"./page.module.css\";\r\n\r\nexport default function About() {\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Navigation */}\r\n      <nav className={styles.navigation}>\r\n        <div className={styles.navContainer}>\r\n          <div className={styles.logo}>\r\n            <h1>Cast Stone</h1>\r\n            <span>Interiors & Decorations</span>\r\n          </div>\r\n          <ul className={styles.navMenu}>\r\n            <li><a href=\"/\">Home</a></li>\r\n            <li><a href=\"/products\">Products</a></li>\r\n            <li><a href=\"/gallery\">Gallery</a></li>\r\n            <li><a href=\"/about\" className={styles.active}>About</a></li>\r\n            <li><a href=\"/contact\">Contact</a></li>\r\n          </ul>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Hero Section */}\r\n      <section className={styles.hero}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>About Cast Stone</h1>\r\n          <p className={styles.heroSubtitle}>\r\n            For over 25 years, we have been crafting exceptional cast stone elements\r\n            that transform ordinary spaces into extraordinary works of art.\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Story Section */}\r\n      <section className={styles.storySection}>\r\n        <div className={styles.storyContainer}>\r\n          <div className={styles.storyContent}>\r\n            <h2>Our Story</h2>\r\n            <p>\r\n              Founded in 1999 by master craftsman Robert Stone, Cast Stone Interiors\r\n              began as a small workshop dedicated to preserving the ancient art of\r\n              cast stone craftsmanship. What started as a passion project has grown\r\n              into one of the most respected names in architectural cast stone.\r\n            </p>\r\n            <p>\r\n              Our commitment to excellence and attention to detail has earned us the\r\n              trust of architects, designers, and homeowners who demand nothing but\r\n              the finest quality for their projects.\r\n            </p>\r\n          </div>\r\n          <div className={styles.storyImage}>\r\n            <Image\r\n              src=\"/images/hero-cast-stone.jpg\"\r\n              alt=\"Cast Stone Workshop\"\r\n              width={600}\r\n              height={400}\r\n              className={styles.storyImg}\r\n            />\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Values Section */}\r\n      <section className={styles.valuesSection}>\r\n        <div className={styles.sectionHeader}>\r\n          <h2>Our Values</h2>\r\n          <p>The principles that guide everything we do</p>\r\n        </div>\r\n        <div className={styles.valuesGrid}>\r\n          <div className={styles.valueCard}>\r\n            <div className={styles.valueIcon}>🎨</div>\r\n            <h3>Craftsmanship</h3>\r\n            <p>Every piece is meticulously handcrafted by skilled artisans who take pride in their work.</p>\r\n          </div>\r\n          <div className={styles.valueCard}>\r\n            <div className={styles.valueIcon}>⚡</div>\r\n            <h3>Quality</h3>\r\n            <p>We use only the finest materials and time-tested techniques to ensure lasting beauty.</p>\r\n          </div>\r\n          <div className={styles.valueCard}>\r\n            <div className={styles.valueIcon}>🤝</div>\r\n            <h3>Service</h3>\r\n            <p>From design consultation to installation, we provide exceptional service every step of the way.</p>\r\n          </div>\r\n          <div className={styles.valueCard}>\r\n            <div className={styles.valueIcon}>🌟</div>\r\n            <h3>Innovation</h3>\r\n            <p>While honoring traditional methods, we embrace new technologies to enhance our craft.</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Team Section */}\r\n      <section className={styles.teamSection}>\r\n        <div className={styles.sectionHeader}>\r\n          <h2>Meet Our Team</h2>\r\n          <p>The master craftsmen behind every creation</p>\r\n        </div>\r\n        <div className={styles.teamGrid}>\r\n          <div className={styles.teamMember}>\r\n            <Image\r\n              src=\"/images/placeholder-video.jpg\"\r\n              alt=\"Robert Stone\"\r\n              width={300}\r\n              height={300}\r\n              className={styles.teamPhoto}\r\n            />\r\n            <h3>Robert Stone</h3>\r\n            <p className={styles.teamRole}>Founder & Master Craftsman</p>\r\n            <p>With over 30 years of experience, Robert leads our team with unmatched expertise in traditional cast stone techniques.</p>\r\n          </div>\r\n          <div className={styles.teamMember}>\r\n            <Image\r\n              src=\"/images/placeholder-video.jpg\"\r\n              alt=\"Maria Rodriguez\"\r\n              width={300}\r\n              height={300}\r\n              className={styles.teamPhoto}\r\n            />\r\n            <h3>Maria Rodriguez</h3>\r\n            <p className={styles.teamRole}>Design Director</p>\r\n            <p>Maria brings artistic vision and modern design sensibilities to every project, ensuring perfect harmony between form and function.</p>\r\n          </div>\r\n          <div className={styles.teamMember}>\r\n            <Image\r\n              src=\"/images/placeholder-video.jpg\"\r\n              alt=\"James Mitchell\"\r\n              width={300}\r\n              height={300}\r\n              className={styles.teamPhoto}\r\n            />\r\n            <h3>James Mitchell</h3>\r\n            <p className={styles.teamRole}>Production Manager</p>\r\n            <p>James oversees our workshop operations, ensuring every piece meets our exacting standards for quality and craftsmanship.</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className={styles.ctaSection}>\r\n        <div className={styles.ctaContent}>\r\n          <h2>Ready to Create Something Beautiful?</h2>\r\n          <p>Let us help you transform your space with our handcrafted cast stone elements.</p>\r\n          <button className={styles.ctaBtn}>Start Your Project</button>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className={styles.footer}>\r\n        <div className={styles.footerContent}>\r\n          <div className={styles.footerSection}>\r\n            <h3>Cast Stone</h3>\r\n            <p>Creating timeless beauty with handcrafted cast stone elements for over 25 years.</p>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Quick Links</h4>\r\n            <ul>\r\n              <li><a href=\"/products\">Products</a></li>\r\n              <li><a href=\"/gallery\">Gallery</a></li>\r\n              <li><a href=\"/about\">About Us</a></li>\r\n              <li><a href=\"/contact\">Contact</a></li>\r\n            </ul>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Contact Info</h4>\r\n            <p>123 Artisan Way<br />Craftsman City, CC 12345</p>\r\n            <p>Phone: (*************</p>\r\n            <p>Email: <EMAIL></p>\r\n          </div>\r\n        </div>\r\n        <div className={styles.footerBottom}>\r\n          <p>&copy; 2024 Cast Stone Interiors. All rights reserved.</p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,8OAAC;gBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;;sCACjC,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAG,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;;8CAC3B,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAI;;;;;;;;;;;8CAChB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAY;;;;;;;;;;;8CACxB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;8CACvB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAS,WAAW,uIAAA,CAAA,UAAM,CAAC,MAAM;kDAAE;;;;;;;;;;;8CAC/C,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,WAAW;;sCAChC,8OAAC;4BAAG,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;sCAAE;;;;;;sCACjC,8OAAC;4BAAE,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;;;;;;;;;;;;0BAQvC,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;0BACrC,cAAA,8OAAC;oBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,cAAc;;sCACnC,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;;8CACjC,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAMH,8OAAC;8CAAE;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;sCAC/B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;0BAOlC,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;kCACtC,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAEL,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;kDAC9B,8OAAC;wCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;kDAAE;;;;;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;kDAC9B,8OAAC;wCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;kDAAE;;;;;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;kDAC9B,8OAAC;wCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;kDAAE;;;;;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;kDAC9B,8OAAC;wCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;kDAAE;;;;;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAMT,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,WAAW;;kCACpC,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;;;;;;;kCAEL,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;;0CAC7B,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;kDAC/B,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;kDAE7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAE,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;kDAAE;;;;;;kDAC/B,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;kDAC/B,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;kDAE7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAE,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;kDAAE;;;;;;kDAC/B,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;kDAC/B,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;kDAE7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAE,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;kDAAE;;;;;;kDAC/B,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAMT,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;sCAC/B,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAO,WAAW,uIAAA,CAAA,UAAM,CAAC,MAAM;sCAAE;;;;;;;;;;;;;;;;;0BAKtC,8OAAC;gBAAO,WAAW,uIAAA,CAAA,UAAM,CAAC,MAAM;;kCAC9B,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;;0DACC,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAY;;;;;;;;;;;0DACxB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;0DACvB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAS;;;;;;;;;;;0DACrB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;0CAG3B,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;;4CAAE;0DAAe,8OAAC;;;;;4CAAK;;;;;;;kDACxB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAGP,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;kCACjC,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}