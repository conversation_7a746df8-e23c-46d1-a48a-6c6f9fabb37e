{"version": 3, "sources": [], "sections": [{"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/contact/page.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n});\n"], "names": [], "mappings": "AAAA;AACA"}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/contact/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport styles from \"./page.module.css\";\r\n\r\nexport default function Contact() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    phone: '',\r\n    projectType: '',\r\n    message: ''\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    // Handle form submission here\r\n    console.log('Form submitted:', formData);\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Navigation */}\r\n      <nav className={styles.navigation}>\r\n        <div className={styles.navContainer}>\r\n          <div className={styles.logo}>\r\n            <h1>Cast Stone</h1>\r\n            <span>Interiors & Decorations</span>\r\n          </div>\r\n          <ul className={styles.navMenu}>\r\n            <li><a href=\"/\">Home</a></li>\r\n            <li><a href=\"/products\">Products</a></li>\r\n            <li><a href=\"/gallery\">Gallery</a></li>\r\n            <li><a href=\"/about\">About</a></li>\r\n            <li><a href=\"/contact\" className={styles.active}>Contact</a></li>\r\n          </ul>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Hero Section */}\r\n      <section className={styles.hero}>\r\n        <div className={styles.heroContent}>\r\n          <h1 className={styles.heroTitle}>Contact Us</h1>\r\n          <p className={styles.heroSubtitle}>\r\n            Ready to transform your space? Get in touch with our team of experts\r\n            to discuss your cast stone project.\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section className={styles.contactSection}>\r\n        <div className={styles.contactContainer}>\r\n          {/* Contact Form */}\r\n          <div className={styles.formContainer}>\r\n            <h2>Start Your Project</h2>\r\n            <p>Fill out the form below and we'll get back to you within 24 hours.</p>\r\n\r\n            <form className={styles.form} onSubmit={handleSubmit}>\r\n              <div className={styles.formRow}>\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"name\" className={styles.label}>\r\n                    Full Name *\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id=\"name\"\r\n                    name=\"name\"\r\n                    className={styles.input}\r\n                    placeholder=\"Your full name\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"email\" className={styles.label}>\r\n                    Email Address *\r\n                  </label>\r\n                  <input\r\n                    type=\"email\"\r\n                    id=\"email\"\r\n                    name=\"email\"\r\n                    className={styles.input}\r\n                    placeholder=\"<EMAIL>\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.formRow}>\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"phone\" className={styles.label}>\r\n                    Phone Number\r\n                  </label>\r\n                  <input\r\n                    type=\"tel\"\r\n                    id=\"phone\"\r\n                    name=\"phone\"\r\n                    className={styles.input}\r\n                    placeholder=\"(*************\"\r\n                    value={formData.phone}\r\n                    onChange={handleChange}\r\n                  />\r\n                </div>\r\n\r\n                <div className={styles.formGroup}>\r\n                  <label htmlFor=\"projectType\" className={styles.label}>\r\n                    Project Type\r\n                  </label>\r\n                  <select\r\n                    id=\"projectType\"\r\n                    name=\"projectType\"\r\n                    className={styles.select}\r\n                    value={formData.projectType}\r\n                    onChange={handleChange}\r\n                  >\r\n                    <option value=\"\">Select project type</option>\r\n                    <option value=\"fireplace\">Fireplace</option>\r\n                    <option value=\"garden\">Garden Features</option>\r\n                    <option value=\"architectural\">Architectural Elements</option>\r\n                    <option value=\"restoration\">Restoration</option>\r\n                    <option value=\"custom\">Custom Design</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.formGroup}>\r\n                <label htmlFor=\"message\" className={styles.label}>\r\n                  Project Details *\r\n                </label>\r\n                <textarea\r\n                  id=\"message\"\r\n                  name=\"message\"\r\n                  rows={6}\r\n                  className={styles.textarea}\r\n                  placeholder=\"Tell us about your project, timeline, and any specific requirements...\"\r\n                  value={formData.message}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <button type=\"submit\" className={styles.submitButton}>\r\n                Send Message\r\n              </button>\r\n            </form>\r\n          </div>\r\n\r\n          {/* Contact Information */}\r\n          <div className={styles.contactInfo}>\r\n            <h2>Get In Touch</h2>\r\n            <p>We're here to help bring your vision to life.</p>\r\n\r\n            <div className={styles.infoGrid}>\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>📍</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Visit Our Workshop</h3>\r\n                  <p className={styles.infoText}>\r\n                    123 Artisan Way<br />\r\n                    Craftsman City, CC 12345\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>📞</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Call Us</h3>\r\n                  <p className={styles.infoText}>(*************</p>\r\n                  <p className={styles.infoSubtext}>Mon-Fri 8AM-6PM</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>✉️</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Email Us</h3>\r\n                  <p className={styles.infoText}><EMAIL></p>\r\n                  <p className={styles.infoSubtext}>We respond within 24 hours</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className={styles.infoItem}>\r\n                <div className={styles.infoIcon}>🕒</div>\r\n                <div>\r\n                  <h3 className={styles.infoTitle}>Business Hours</h3>\r\n                  <p className={styles.infoText}>\r\n                    Monday - Friday: 8:00 AM - 6:00 PM<br />\r\n                    Saturday: 9:00 AM - 4:00 PM<br />\r\n                    Sunday: Closed\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className={styles.footer}>\r\n        <div className={styles.footerContent}>\r\n          <div className={styles.footerSection}>\r\n            <h3>Cast Stone</h3>\r\n            <p>Creating timeless beauty with handcrafted cast stone elements for over 25 years.</p>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Quick Links</h4>\r\n            <ul>\r\n              <li><a href=\"/products\">Products</a></li>\r\n              <li><a href=\"/gallery\">Gallery</a></li>\r\n              <li><a href=\"/about\">About Us</a></li>\r\n              <li><a href=\"/contact\">Contact</a></li>\r\n            </ul>\r\n          </div>\r\n          <div className={styles.footerSection}>\r\n            <h4>Contact Info</h4>\r\n            <p>123 Artisan Way<br />Craftsman City, CC 12345</p>\r\n            <p>Phone: (*************</p>\r\n            <p>Email: <EMAIL></p>\r\n          </div>\r\n        </div>\r\n        <div className={styles.footerBottom}>\r\n          <p>&copy; 2024 Cast Stone Interiors. All rights reserved.</p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,aAAa;QACb,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,8OAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;sCACjC,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,OAAO;;8CAC3B,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAI;;;;;;;;;;;8CAChB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAY;;;;;;;;;;;8CACxB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAW;;;;;;;;;;;8CACvB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;8CACrB,8OAAC;8CAAG,cAAA,8OAAC;wCAAE,MAAK;wCAAW,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;sCAChC,8OAAC;4BAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sCAAE;;;;;;sCACjC,8OAAC;4BAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;;;;;;;;;;;;0BAQvC,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;0BACvC,cAAA,8OAAC;oBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,gBAAgB;;sCAErC,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;8CAClC,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAEH,8OAAC;oCAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;oCAAE,UAAU;;sDACtC,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,8OAAC;4DAAM,SAAQ;4DAAO,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAG/C,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;4DACvB,aAAY;4DACZ,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,8OAAC;4DAAM,SAAQ;4DAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAGhD,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;4DACvB,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,8OAAC;4DAAM,SAAQ;4DAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAGhD,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;4DACvB,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;sEAC9B,8OAAC;4DAAM,SAAQ;4DAAc,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAGtD,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;4DACxB,OAAO,SAAS,WAAW;4DAC3B,UAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,8OAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,8OAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,8OAAC;oEAAO,OAAM;8EAAgB;;;;;;8EAC9B,8OAAC;oEAAO,OAAM;8EAAc;;;;;;8EAC5B,8OAAC;oEAAO,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAK7B,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;8DAC9B,8OAAC;oDAAM,SAAQ;oDAAU,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;8DAAE;;;;;;8DAGlD,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;oDAC1B,aAAY;oDACZ,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;4CAAO,MAAK;4CAAS,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;;;;;;;;;;;;;sCAO1D,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;8CAChC,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAEH,8OAAC;oCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC7B,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,8OAAC;4DAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;gEAAE;8EACd,8OAAC;;;;;gEAAK;;;;;;;;;;;;;;;;;;;sDAM3B,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,8OAAC;4DAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;sEAAE;;;;;;sEAC/B,8OAAC;4DAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;sEAAE;;;;;;;;;;;;;;;;;;sDAItC,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,8OAAC;4DAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;sEAAE;;;;;;sEAC/B,8OAAC;4DAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;sEAAE;;;;;;;;;;;;;;;;;;sDAItC,8OAAC;4CAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;8DAC7B,8OAAC;oDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;8DAAE;;;;;;8DACjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sEAAE;;;;;;sEACjC,8OAAC;4DAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;gEAAE;8EACK,8OAAC;;;;;gEAAK;8EACb,8OAAC;;;;;gEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW/C,8OAAC;gBAAO,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;;kCAC9B,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;;0DACC,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAY;;;;;;;;;;;0DACxB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;0DACvB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAS;;;;;;;;;;;0DACrB,8OAAC;0DAAG,cAAA,8OAAC;oDAAE,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;0CAG3B,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;;4CAAE;0DAAe,8OAAC;;;;;4CAAK;;;;;;;kDACxB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAGP,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;kCACjC,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}