{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/page.module.css"], "sourcesContent": ["/* Cast Stone Homepage Styles */\r\n\r\n.container {\r\n  min-height: 100vh;\r\n  background: var(--background-light);\r\n  color: var(--text-dark);\r\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* Navigation */\r\n.navigation {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 1000;\r\n  padding: 1rem 0;\r\n  border-bottom: 1px solid rgba(139, 69, 19, 0.1);\r\n}\r\n\r\n.navContainer {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo h1 {\r\n  font-size: 1.8rem;\r\n  font-weight: 700;\r\n  color: var(--primary-color);\r\n  margin: 0;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.logo span {\r\n  font-size: 0.9rem;\r\n  color: var(--text-light);\r\n  font-weight: 400;\r\n}\r\n\r\n.navMenu {\r\n  display: flex;\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  gap: 2rem;\r\n}\r\n\r\n.navMenu a {\r\n  text-decoration: none;\r\n  color: var(--text-dark);\r\n  font-weight: 500;\r\n  transition: color 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.navMenu a:hover {\r\n  color: var(--primary-color);\r\n}\r\n\r\n.navMenu a::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 0;\r\n  width: 0;\r\n  height: 2px;\r\n  background: var(--primary-color);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.navMenu a:hover::after {\r\n  width: 100%;\r\n}\r\n\r\n/* Hero Section */\r\n.hero {\r\n  padding: 8rem 2rem 4rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  align-items: center;\r\n  min-height: 80vh;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .hero {\r\n    grid-template-columns: 1fr;\r\n    text-align: center;\r\n    padding: 6rem 1rem 3rem;\r\n  }\r\n}\r\n\r\n.heroContent {\r\n  max-width: 500px;\r\n}\r\n\r\n.heroTitle {\r\n  font-size: 3.5rem;\r\n  font-weight: 800;\r\n  line-height: 1.1;\r\n  margin-bottom: 1.5rem;\r\n  color: var(--text-dark);\r\n}\r\n\r\n.highlight {\r\n  color: var(--primary-color);\r\n  position: relative;\r\n}\r\n\r\n.highlight::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));\r\n}\r\n\r\n.heroSubtitle {\r\n  font-size: 1.2rem;\r\n  color: var(--text-light);\r\n  margin-bottom: 2.5rem;\r\n  line-height: 1.7;\r\n}\r\n\r\n.heroActions {\r\n  display: flex;\r\n  gap: 1rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.primaryBtn {\r\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\r\n  color: white;\r\n  border: none;\r\n  padding: 1rem 2rem;\r\n  border-radius: 50px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n.primaryBtn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-hover);\r\n}\r\n\r\n.secondaryBtn {\r\n  background: transparent;\r\n  color: var(--primary-color);\r\n  border: 2px solid var(--primary-color);\r\n  padding: 1rem 2rem;\r\n  border-radius: 50px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.secondaryBtn:hover {\r\n  background: var(--primary-color);\r\n  color: white;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.heroImage {\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 20px;\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n.heroImg {\r\n  width: 100%;\r\n  height: auto;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.heroImage:hover .heroImg {\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* Section Headers */\r\n.sectionHeader {\r\n  text-align: center;\r\n  margin-bottom: 4rem;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.sectionHeader h2 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--text-dark);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.sectionHeader p {\r\n  font-size: 1.1rem;\r\n  color: var(--text-light);\r\n}\r\n\r\n/* Video Carousel Section */\r\n.videoSection {\r\n  padding: 6rem 2rem;\r\n  background: white;\r\n  overflow: hidden;\r\n}\r\n\r\n.videoCarousel {\r\n  position: relative;\r\n  max-width: 1000px;\r\n  margin: 0 auto;\r\n  height: 500px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.videoSlide {\r\n  position: absolute;\r\n  width: 100%;\r\n  opacity: 0;\r\n  transform: translateX(100px);\r\n  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);\r\n  pointer-events: none;\r\n}\r\n\r\n.videoSlide.active {\r\n  opacity: 1;\r\n  transform: translateX(0);\r\n  pointer-events: all;\r\n}\r\n\r\n.slideFromLeft {\r\n  transform: translateX(-100px);\r\n}\r\n\r\n.slideFromLeft.active {\r\n  transform: translateX(0);\r\n}\r\n\r\n.slideFromRight {\r\n  transform: translateX(100px);\r\n}\r\n\r\n.slideFromRight.active {\r\n  transform: translateX(0);\r\n}\r\n\r\n.videoContainer {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 3rem;\r\n  align-items: center;\r\n  max-width: 900px;\r\n  margin: 0 auto;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .videoContainer {\r\n    grid-template-columns: 1fr;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.videoPlaceholder {\r\n  position: relative;\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow);\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.videoPlaceholder:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: var(--shadow-hover);\r\n}\r\n\r\n.videoThumbnail {\r\n  width: 100%;\r\n  height: 300px;\r\n  object-fit: cover;\r\n}\r\n\r\n.playButton {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.videoPlaceholder:hover .playButton {\r\n  transform: translate(-50%, -50%) scale(1.1);\r\n}\r\n\r\n.videoInfo h3 {\r\n  font-size: 1.8rem;\r\n  font-weight: 700;\r\n  color: var(--text-dark);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.videoInfo p {\r\n  font-size: 1.1rem;\r\n  color: var(--text-light);\r\n  line-height: 1.6;\r\n}\r\n\r\n.videoControls {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 1rem;\r\n  margin-top: 3rem;\r\n}\r\n\r\n.videoDot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(139, 69, 19, 0.3);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.videoDot.activeDot {\r\n  background: var(--primary-color);\r\n  transform: scale(1.2);\r\n}\r\n\r\n.videoDot:hover {\r\n  background: var(--primary-color);\r\n}\r\n\r\n/* Featured Products */\r\n.featuredProducts {\r\n  padding: 6rem 2rem;\r\n  background: var(--background-light);\r\n}\r\n\r\n.productGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.productCard {\r\n  background: white;\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  box-shadow: var(--shadow);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.productCard:hover {\r\n  transform: translateY(-10px);\r\n  box-shadow: var(--shadow-hover);\r\n}\r\n\r\n.productImage {\r\n  width: 100%;\r\n  height: 250px;\r\n  object-fit: cover;\r\n}\r\n\r\n.productInfo {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.productInfo h3 {\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  color: var(--text-dark);\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.productInfo p {\r\n  color: var(--text-light);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.productPrice {\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n  color: var(--primary-color);\r\n}\r\n\r\n/* Newsletter */\r\n.newsletter {\r\n  padding: 4rem 2rem;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\r\n  color: white;\r\n  text-align: center;\r\n}\r\n\r\n.newsletterContent h2 {\r\n  font-size: 2.2rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.newsletterContent p {\r\n  font-size: 1.1rem;\r\n  margin-bottom: 2rem;\r\n  opacity: 0.9;\r\n}\r\n\r\n.newsletterForm {\r\n  display: flex;\r\n  max-width: 500px;\r\n  margin: 0 auto;\r\n  gap: 1rem;\r\n}\r\n\r\n@media (max-width: 640px) {\r\n  .newsletterForm {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n.emailInput {\r\n  flex: 1;\r\n  padding: 1rem 1.5rem;\r\n  border: none;\r\n  border-radius: 50px;\r\n  font-size: 1rem;\r\n  outline: none;\r\n}\r\n\r\n.subscribeBtn {\r\n  background: white;\r\n  color: var(--primary-color);\r\n  border: none;\r\n  padding: 1rem 2rem;\r\n  border-radius: 50px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  white-space: nowrap;\r\n}\r\n\r\n.subscribeBtn:hover {\r\n  background: var(--background-light);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Footer */\r\n.footer {\r\n  background: var(--text-dark);\r\n  color: white;\r\n  padding: 3rem 2rem 1rem;\r\n}\r\n\r\n.footerContent {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.footerSection h3 {\r\n  font-size: 1.3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerSection h4 {\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerSection p {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  line-height: 1.6;\r\n}\r\n\r\n.footerSection ul {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.footerSection ul li {\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.footerSection ul li a {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-decoration: none;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.footerSection ul li a:hover {\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerBottom {\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  padding-top: 1rem;\r\n  text-align: center;\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .heroTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .sectionHeader h2 {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .navMenu {\r\n    display: none;\r\n  }\r\n\r\n  .videoCarousel {\r\n    height: auto;\r\n  }\r\n\r\n  .videoSlide {\r\n    position: relative;\r\n    opacity: 1;\r\n    transform: none;\r\n  }\r\n\r\n  .videoSlide.active {\r\n    display: block;\r\n  }\r\n\r\n  .videoSlide:not(.active) {\r\n    display: none;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAEA;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAKA;;;;;;;;;;;AAWA;EACE;;;;;;;AAOF;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;AAKA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;EACE;;;;;;AAMF;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAKA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;EACE;;;;;AAKF;;;;;;;;;AASA;;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;AAMA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA", "debugId": null}}]}