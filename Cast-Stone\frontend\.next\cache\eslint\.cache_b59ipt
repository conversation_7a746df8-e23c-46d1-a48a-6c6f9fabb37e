[{"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\gallery\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\products\\page.tsx": "6"}, {"size": 7212, "mtime": 1750672936543, "results": "7", "hashOfConfig": "8"}, {"size": 8808, "mtime": 1750672936544, "results": "9", "hashOfConfig": "8"}, {"size": 7886, "mtime": 1750675404856, "results": "10", "hashOfConfig": "8"}, {"size": 723, "mtime": 1750672936549, "results": "11", "hashOfConfig": "8"}, {"size": 8767, "mtime": 1750672936550, "results": "12", "hashOfConfig": "8"}, {"size": 7036, "mtime": 1750672936552, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "10gt0ic", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\about\\page.tsx", ["32"], [], "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\contact\\page.tsx", ["33", "34", "35"], [], "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\gallery\\page.tsx", ["36"], [], "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\page.tsx", ["37"], [], "C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\products\\page.tsx", ["38"], [], {"ruleId": "39", "severity": 2, "message": "40", "line": 15, "column": 17, "nodeType": "41", "endLine": 15, "endColumn": 29}, {"ruleId": "39", "severity": 2, "message": "40", "line": 38, "column": 17, "nodeType": "41", "endLine": 38, "endColumn": 29}, {"ruleId": "42", "severity": 2, "message": "43", "line": 64, "column": 46, "nodeType": "44", "messageId": "45", "suggestions": "46"}, {"ruleId": "42", "severity": 2, "message": "43", "line": 163, "column": 18, "nodeType": "44", "messageId": "45", "suggestions": "47"}, {"ruleId": "39", "severity": 2, "message": "40", "line": 107, "column": 17, "nodeType": "41", "endLine": 107, "endColumn": 29}, {"ruleId": "39", "severity": 2, "message": "40", "line": 51, "column": 17, "nodeType": "41", "endLine": 51, "endColumn": 29}, {"ruleId": "39", "severity": 2, "message": "40", "line": 99, "column": 17, "nodeType": "41", "endLine": 99, "endColumn": 29}, "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["48", "49", "50", "51"], ["52", "53", "54", "55"], {"messageId": "56", "data": "57", "fix": "58", "desc": "59"}, {"messageId": "56", "data": "60", "fix": "61", "desc": "62"}, {"messageId": "56", "data": "63", "fix": "64", "desc": "65"}, {"messageId": "56", "data": "66", "fix": "67", "desc": "68"}, {"messageId": "56", "data": "69", "fix": "70", "desc": "59"}, {"messageId": "56", "data": "71", "fix": "72", "desc": "62"}, {"messageId": "56", "data": "73", "fix": "74", "desc": "65"}, {"messageId": "56", "data": "75", "fix": "76", "desc": "68"}, "replaceWithAlt", {"alt": "77"}, {"range": "78", "text": "79"}, "Replace with `&apos;`.", {"alt": "80"}, {"range": "81", "text": "82"}, "Replace with `&lsquo;`.", {"alt": "83"}, {"range": "84", "text": "85"}, "Replace with `&#39;`.", {"alt": "86"}, {"range": "87", "text": "88"}, "Replace with `&rsquo;`.", {"alt": "77"}, {"range": "89", "text": "90"}, {"alt": "80"}, {"range": "91", "text": "92"}, {"alt": "83"}, {"range": "93", "text": "94"}, {"alt": "86"}, {"range": "95", "text": "96"}, "&apos;", [2003, 2069], "Fill out the form below and we&apos;ll get back to you within 24 hours.", "&lsquo;", [2003, 2069], "Fill out the form below and we&lsquo;ll get back to you within 24 hours.", "&#39;", [2003, 2069], "Fill out the form below and we&#39;ll get back to you within 24 hours.", "&rsquo;", [2003, 2069], "Fill out the form below and we&rsquo;ll get back to you within 24 hours.", [5819, 5864], "We&apos;re here to help bring your vision to life.", [5819, 5864], "We&lsquo;re here to help bring your vision to life.", [5819, 5864], "We&#39;re here to help bring your vision to life.", [5819, 5864], "We&rsquo;re here to help bring your vision to life."]