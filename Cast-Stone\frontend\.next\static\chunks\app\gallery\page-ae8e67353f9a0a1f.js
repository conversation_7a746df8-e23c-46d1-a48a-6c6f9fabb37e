(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[235],{1469:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),!function(e,a){for(var t in a)Object.defineProperty(e,t,{enumerable:!0,get:a[t]})}(a,{default:function(){return s},getImageProps:function(){return o}});let i=t(8229),r=t(8883),n=t(3063),l=i._(t(1193));function o(e){let{props:a}=(0,r.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(a))void 0===t&&delete a[e];return{props:a}}let s=n.Image},5333:e=>{e.exports={container:"page_container__SJ3eX",navigation:"page_navigation__7Xih5",navContainer:"page_navContainer__254Hx",logo:"page_logo__Daztz",navMenu:"page_navMenu__803g3",active:"page_active__Zgl2f",hero:"page_hero__0ADgs",heroContent:"page_heroContent__w_g3m",heroTitle:"page_heroTitle__Wwj4X",heroSubtitle:"page_heroSubtitle__EOVwn",gallerySection:"page_gallerySection__Hvnk3",categoryFilter:"page_categoryFilter__r_Gq1",categoryBtn:"page_categoryBtn__W3Lb9",galleryGrid:"page_galleryGrid__xmki5",galleryItem:"page_galleryItem__AEEtc",imageContainer:"page_imageContainer__rJCPg",galleryImage:"page_galleryImage__c71jN",imageOverlay:"page_imageOverlay__37WH_",imageTitle:"page_imageTitle__1alQj",imageDescription:"page_imageDescription__Q7ikL",viewBtn:"page_viewBtn__SvAw4",modal:"page_modal___R1Jk",modalContent:"page_modalContent__foNZy",closeBtn:"page_closeBtn__lpJcZ",modalImage:"page_modalImage__69QE6",modalInfo:"page_modalInfo__CGhPa",ctaSection:"page_ctaSection__lBYMe",ctaContent:"page_ctaContent__PzhgG",ctaBtn:"page_ctaBtn__PSi7x",footer:"page_footer__mc4F5",footerContent:"page_footerContent__y0gmI",footerSection:"page_footerSection__LBmn8",footerBottom:"page_footerBottom__FDXFF"}},6129:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});var i=t(5155),r=t(2115),n=t(6766),l=t(5333),o=t.n(l);function s(){let[e,a]=(0,r.useState)("all"),[t,l]=(0,r.useState)(null),s=[{id:1,title:"Luxury Living Room Fireplace",category:"residential",image:"/images/fireplace-collection.jpg",description:"Custom cast stone fireplace with intricate carved details"},{id:2,title:"Grand Hotel Entrance",category:"commercial",image:"/images/architectural-collection.jpg",description:"Majestic columns and architectural elements for hotel lobby"},{id:3,title:"Private Garden Fountain",category:"garden",image:"/images/garden-collection.jpg",description:"Three-tier fountain centerpiece for estate garden"},{id:4,title:"Historic Building Restoration",category:"restoration",image:"/images/architectural-collection.jpg",description:"Faithful reproduction of original cast stone elements"},{id:5,title:"Modern Fireplace Design",category:"residential",image:"/images/fireplace-collection.jpg",description:"Contemporary cast stone fireplace with clean lines"},{id:6,title:"Corporate Headquarters",category:"commercial",image:"/images/architectural-collection.jpg",description:"Impressive facade elements for corporate building"},{id:7,title:"Estate Garden Features",category:"garden",image:"/images/garden-collection.jpg",description:"Complete garden transformation with cast stone elements"},{id:8,title:"Victorian Home Restoration",category:"restoration",image:"/images/architectural-collection.jpg",description:"Period-accurate cast stone details for historic home"},{id:9,title:"Luxury Master Suite",category:"residential",image:"/images/fireplace-collection.jpg",description:"Elegant bedroom fireplace with custom surround"}],c="all"===e?s:s.filter(a=>a.category===e);return(0,i.jsxs)("div",{className:o().container,children:[(0,i.jsx)("nav",{className:o().navigation,children:(0,i.jsxs)("div",{className:o().navContainer,children:[(0,i.jsxs)("div",{className:o().logo,children:[(0,i.jsx)("h1",{children:"Cast Stone"}),(0,i.jsx)("span",{children:"Interiors & Decorations"})]}),(0,i.jsxs)("ul",{className:o().navMenu,children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/",children:"Home"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/products",children:"Products"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/gallery",className:o().active,children:"Gallery"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/about",children:"About"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/contact",children:"Contact"})})]})]})}),(0,i.jsx)("section",{className:o().hero,children:(0,i.jsxs)("div",{className:o().heroContent,children:[(0,i.jsx)("h1",{className:o().heroTitle,children:"Project Gallery"}),(0,i.jsx)("p",{className:o().heroSubtitle,children:"Explore our portfolio of stunning cast stone installations, from intimate residential fireplaces to grand commercial projects."})]})}),(0,i.jsxs)("section",{className:o().gallerySection,children:[(0,i.jsx)("div",{className:o().categoryFilter,children:[{id:"all",name:"All Projects"},{id:"residential",name:"Residential"},{id:"commercial",name:"Commercial"},{id:"garden",name:"Garden & Outdoor"},{id:"restoration",name:"Restoration"}].map(t=>(0,i.jsx)("button",{className:"".concat(o().categoryBtn," ").concat(e===t.id?o().active:""),onClick:()=>a(t.id),children:t.name},t.id))}),(0,i.jsx)("div",{className:o().galleryGrid,children:c.map(e=>(0,i.jsx)("div",{className:o().galleryItem,onClick:()=>l(e),children:(0,i.jsxs)("div",{className:o().imageContainer,children:[(0,i.jsx)(n.default,{src:e.image,alt:e.title,width:400,height:300,className:o().galleryImage}),(0,i.jsxs)("div",{className:o().imageOverlay,children:[(0,i.jsx)("h3",{className:o().imageTitle,children:e.title}),(0,i.jsx)("p",{className:o().imageDescription,children:e.description}),(0,i.jsx)("button",{className:o().viewBtn,children:"View Details"})]})]})},e.id))})]}),t&&(0,i.jsx)("div",{className:o().modal,onClick:()=>l(null),children:(0,i.jsxs)("div",{className:o().modalContent,onClick:e=>e.stopPropagation(),children:[(0,i.jsx)("button",{className:o().closeBtn,onClick:()=>l(null),children:"\xd7"}),(0,i.jsx)(n.default,{src:t.image,alt:t.title,width:800,height:600,className:o().modalImage}),(0,i.jsxs)("div",{className:o().modalInfo,children:[(0,i.jsx)("h2",{children:t.title}),(0,i.jsx)("p",{children:t.description})]})]})}),(0,i.jsx)("section",{className:o().ctaSection,children:(0,i.jsxs)("div",{className:o().ctaContent,children:[(0,i.jsx)("h2",{children:"Ready to Start Your Project?"}),(0,i.jsx)("p",{children:"Let our master craftsmen bring your vision to life with custom cast stone creations."}),(0,i.jsx)("button",{className:o().ctaBtn,children:"Get Started Today"})]})}),(0,i.jsxs)("footer",{className:o().footer,children:[(0,i.jsxs)("div",{className:o().footerContent,children:[(0,i.jsxs)("div",{className:o().footerSection,children:[(0,i.jsx)("h3",{children:"Cast Stone"}),(0,i.jsx)("p",{children:"Creating timeless beauty with handcrafted cast stone elements for over 25 years."})]}),(0,i.jsxs)("div",{className:o().footerSection,children:[(0,i.jsx)("h4",{children:"Quick Links"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/products",children:"Products"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/gallery",children:"Gallery"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/about",children:"About Us"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/contact",children:"Contact"})})]})]}),(0,i.jsxs)("div",{className:o().footerSection,children:[(0,i.jsx)("h4",{children:"Contact Info"}),(0,i.jsxs)("p",{children:["123 Artisan Way",(0,i.jsx)("br",{}),"Craftsman City, CC 12345"]}),(0,i.jsx)("p",{children:"Phone: (*************"}),(0,i.jsx)("p",{children:"Email: <EMAIL>"})]})]}),(0,i.jsx)("div",{className:o().footerBottom,children:(0,i.jsx)("p",{children:"\xa9 2024 Cast Stone Interiors. All rights reserved."})})]})]})}},6766:(e,a,t)=>{"use strict";t.d(a,{default:()=>r.a});var i=t(1469),r=t.n(i)},7858:(e,a,t)=>{Promise.resolve().then(t.bind(t,6129))}},e=>{var a=a=>e(e.s=a);e.O(0,[22,63,441,684,358],()=>a(7858)),_N_E=e.O()}]);