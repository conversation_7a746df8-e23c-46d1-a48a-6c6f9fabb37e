/* [project]/src/app/about/page.module.css [app-client] (css) */
.page-module__NfDiEG__container {
  background: var(--background-light);
  min-height: 100vh;
  color: var(--text-dark);
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
}

.page-module__NfDiEG__navigation {
  backdrop-filter: blur(10px);
  z-index: 1000;
  background: #fffffff2;
  border-bottom: 1px solid #8b45131a;
  padding: 1rem 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.page-module__NfDiEG__navContainer {
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
}

.page-module__NfDiEG__logo h1 {
  color: var(--primary-color);
  letter-spacing: -.02em;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-module__NfDiEG__logo span {
  color: var(--text-light);
  font-size: .9rem;
  font-weight: 400;
}

.page-module__NfDiEG__navMenu {
  gap: 2rem;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.page-module__NfDiEG__navMenu a {
  color: var(--text-dark);
  font-weight: 500;
  text-decoration: none;
  transition: color .3s;
  position: relative;
}

.page-module__NfDiEG__navMenu a:hover, .page-module__NfDiEG__navMenu a.page-module__NfDiEG__active {
  color: var(--primary-color);
}

.page-module__NfDiEG__navMenu a:after {
  content: "";
  background: var(--primary-color);
  width: 0;
  height: 2px;
  transition: width .3s;
  position: absolute;
  bottom: -5px;
  left: 0;
}

.page-module__NfDiEG__navMenu a:hover:after, .page-module__NfDiEG__navMenu a.page-module__NfDiEG__active:after {
  width: 100%;
}

.page-module__NfDiEG__hero {
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: #fff;
  padding: 8rem 2rem 4rem;
}

.page-module__NfDiEG__heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.page-module__NfDiEG__heroTitle {
  margin-bottom: 1.5rem;
  font-size: 3.5rem;
  font-weight: 800;
}

.page-module__NfDiEG__heroSubtitle {
  opacity: .9;
  font-size: 1.2rem;
  line-height: 1.7;
}

.page-module__NfDiEG__storySection {
  background: #fff;
  padding: 6rem 2rem;
}

.page-module__NfDiEG__storyContainer {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

@media (width <= 768px) {
  .page-module__NfDiEG__storyContainer {
    text-align: center;
    grid-template-columns: 1fr;
  }
}

.page-module__NfDiEG__storyContent h2 {
  color: var(--text-dark);
  margin-bottom: 2rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module__NfDiEG__storyContent p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.7;
}

.page-module__NfDiEG__storyImage {
  box-shadow: var(--shadow);
  border-radius: 15px;
  overflow: hidden;
}

.page-module__NfDiEG__storyImg {
  object-fit: cover;
  width: 100%;
  height: auto;
}

.page-module__NfDiEG__sectionHeader {
  text-align: center;
  max-width: 600px;
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
}

.page-module__NfDiEG__sectionHeader h2 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module__NfDiEG__sectionHeader p {
  color: var(--text-light);
  font-size: 1.1rem;
}

.page-module__NfDiEG__valuesSection {
  background: var(--background-light);
  padding: 6rem 2rem;
}

.page-module__NfDiEG__valuesGrid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.page-module__NfDiEG__valueCard {
  text-align: center;
  box-shadow: var(--shadow);
  background: #fff;
  border-radius: 15px;
  padding: 2rem;
  transition: all .3s;
}

.page-module__NfDiEG__valueCard:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-5px);
}

.page-module__NfDiEG__valueIcon {
  margin-bottom: 1rem;
  font-size: 3rem;
}

.page-module__NfDiEG__valueCard h3 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.page-module__NfDiEG__valueCard p {
  color: var(--text-light);
  line-height: 1.6;
}

.page-module__NfDiEG__teamSection {
  background: #fff;
  padding: 6rem 2rem;
}

.page-module__NfDiEG__teamGrid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.page-module__NfDiEG__teamMember {
  text-align: center;
  background: var(--background-light);
  border-radius: 15px;
  padding: 2rem;
  transition: all .3s;
}

.page-module__NfDiEG__teamMember:hover {
  box-shadow: var(--shadow);
  transform: translateY(-5px);
}

.page-module__NfDiEG__teamPhoto {
  object-fit: cover;
  border: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 200px;
  height: 200px;
  margin-bottom: 1.5rem;
}

.page-module__NfDiEG__teamMember h3 {
  color: var(--text-dark);
  margin-bottom: .5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.page-module__NfDiEG__teamRole {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.page-module__NfDiEG__teamMember p:not(.page-module__NfDiEG__teamRole) {
  color: var(--text-light);
  line-height: 1.6;
}

.page-module__NfDiEG__ctaSection {
  background: var(--primary-color);
  color: #fff;
  text-align: center;
  padding: 4rem 2rem;
}

.page-module__NfDiEG__ctaContent h2 {
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module__NfDiEG__ctaContent p {
  opacity: .9;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.page-module__NfDiEG__ctaBtn {
  color: var(--primary-color);
  cursor: pointer;
  background: #fff;
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__NfDiEG__ctaBtn:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.page-module__NfDiEG__footer {
  background: var(--text-dark);
  color: #fff;
  padding: 3rem 2rem 1rem;
}

.page-module__NfDiEG__footerContent {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto 2rem;
  display: grid;
}

.page-module__NfDiEG__footerSection h3 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.page-module__NfDiEG__footerSection h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.page-module__NfDiEG__footerSection p {
  color: #fffc;
  line-height: 1.6;
}

.page-module__NfDiEG__footerSection ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__NfDiEG__footerSection ul li {
  margin-bottom: .5rem;
}

.page-module__NfDiEG__footerSection ul li a {
  color: #fffc;
  text-decoration: none;
  transition: color .3s;
}

.page-module__NfDiEG__footerSection ul li a:hover {
  color: var(--secondary-color);
}

.page-module__NfDiEG__footerBottom {
  text-align: center;
  color: #fff9;
  border-top: 1px solid #ffffff1a;
  padding-top: 1rem;
}

@media (width <= 768px) {
  .page-module__NfDiEG__heroTitle {
    font-size: 2.5rem;
  }

  .page-module__NfDiEG__navMenu {
    display: none;
  }

  .page-module__NfDiEG__valuesGrid, .page-module__NfDiEG__teamGrid {
    grid-template-columns: 1fr;
  }

  .page-module__NfDiEG__storyContent h2 {
    font-size: 2rem;
  }
}


/*# sourceMappingURL=src_app_about_page_module_c798c172.css.map*/