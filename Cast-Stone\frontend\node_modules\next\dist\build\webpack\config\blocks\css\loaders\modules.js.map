{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/modules.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { ConfigurationContext } from '../../../utils'\nimport { getClientStyleLoader } from './client'\nimport { cssFileResolve } from './file-resolve'\nimport { getCssModuleLocalIdent } from './getCssModuleLocalIdent'\n\nexport function getCssModuleLoader(\n  ctx: ConfigurationContext,\n  postcss: any,\n  preProcessors: readonly webpack.RuleSetUseItem[] = []\n): webpack.RuleSetUseItem[] {\n  const loaders: webpack.RuleSetUseItem[] = []\n\n  if (ctx.isClient) {\n    // Add appropriate development more or production mode style\n    // loader\n    loaders.push(\n      getClientStyleLoader({\n        hasAppDir: ctx.hasAppDir,\n        isAppDir: ctx.isAppDir,\n        isDevelopment: ctx.isDevelopment,\n        assetPrefix: ctx.assetPrefix,\n      })\n    )\n  }\n\n  if (ctx.experimental.useLightningcss) {\n    loaders.push({\n      loader: require.resolve('../../../../loaders/lightningcss-loader/src'),\n      options: {\n        importLoaders: 1 + preProcessors.length,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        modules: {\n          // Do not transform class names (CJS mode backwards compatibility):\n          exportLocalsConvention: 'asIs',\n          // Server-side (Node.js) rendering support:\n          exportOnlyLocals: ctx.isServer,\n        },\n        targets: ctx.supportedBrowsers,\n        postcss,\n      },\n    })\n  } else {\n    // Resolve CSS `@import`s and `url()`s\n    loaders.push({\n      loader: require.resolve('../../../../loaders/css-loader/src'),\n      options: {\n        postcss,\n        importLoaders: 1 + preProcessors.length,\n        // Use CJS mode for backwards compatibility:\n        esModule: false,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        modules: {\n          // Do not transform class names (CJS mode backwards compatibility):\n          exportLocalsConvention: 'asIs',\n          // Server-side (Node.js) rendering support:\n          exportOnlyLocals: ctx.isServer,\n          // Disallow global style exports so we can code-split CSS and\n          // not worry about loading order.\n          mode: 'pure',\n          // Generate a friendly production-ready name so it's\n          // reasonably understandable. The same name is used for\n          // development.\n          // TODO: Consider making production reduce this to a single\n          // character?\n          getLocalIdent: getCssModuleLocalIdent,\n        },\n      },\n    })\n\n    // Compile CSS\n    loaders.push({\n      loader: require.resolve('../../../../loaders/postcss-loader/src'),\n      options: {\n        postcss,\n      },\n    })\n  }\n\n  loaders.push(\n    // Webpack loaders run like a stack, so we need to reverse the natural\n    // order of preprocessors.\n    ...preProcessors.slice().reverse()\n  )\n\n  return loaders\n}\n"], "names": ["getCssModuleLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "getClientStyleLoader", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "experimental", "useLightningcss", "loader", "require", "resolve", "options", "importLoaders", "length", "url", "resourcePath", "cssFileResolve", "urlImports", "import", "_", "modules", "exportLocalsConvention", "exportOnlyLocals", "isServer", "targets", "supportedBrowsers", "esModule", "mode", "getLocalIdent", "getCssModuleLocalIdent", "slice", "reverse"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;wBAJqB;6BACN;wCACQ;AAEhC,SAASA,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVC,IAAAA,4BAAoB,EAAC;YACnBC,WAAWP,IAAIO,SAAS;YACxBC,UAAUR,IAAIQ,QAAQ;YACtBC,eAAeT,IAAIS,aAAa;YAChCC,aAAaV,IAAIU,WAAW;QAC9B;IAEJ;IAEA,IAAIV,IAAIW,YAAY,CAACC,eAAe,EAAE;QACpCT,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPC,eAAe,IAAIf,cAAcgB,MAAM;gBACvCC,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DG,SAAS;oBACP,mEAAmE;oBACnEC,wBAAwB;oBACxB,2CAA2C;oBAC3CC,kBAAkB3B,IAAI4B,QAAQ;gBAChC;gBACAC,SAAS7B,IAAI8B,iBAAiB;gBAC9B7B;YACF;QACF;IACF,OAAO;QACL,sCAAsC;QACtCE,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPf;gBACAgB,eAAe,IAAIf,cAAcgB,MAAM;gBACvC,4CAA4C;gBAC5Ca,UAAU;gBACVZ,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DC,QAAQ,CAACJ,KAAaK,GAAQJ,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcpB,IAAIW,YAAY,CAACW,UAAU;gBAC/DG,SAAS;oBACP,mEAAmE;oBACnEC,wBAAwB;oBACxB,2CAA2C;oBAC3CC,kBAAkB3B,IAAI4B,QAAQ;oBAC9B,6DAA6D;oBAC7D,iCAAiC;oBACjCI,MAAM;oBACN,oDAAoD;oBACpD,uDAAuD;oBACvD,eAAe;oBACf,2DAA2D;oBAC3D,aAAa;oBACbC,eAAeC,8CAAsB;gBACvC;YACF;QACF;QAEA,cAAc;QACd/B,QAAQE,IAAI,CAAC;YACXQ,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPf;YACF;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAciC,KAAK,GAAGC,OAAO;IAGlC,OAAOjC;AACT"}