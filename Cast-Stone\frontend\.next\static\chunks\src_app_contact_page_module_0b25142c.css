/* [project]/src/app/contact/page.module.css [app-client] (css) */
.page-module__OSLHOG__container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  display: flex;
}

.page-module__OSLHOG__main {
  background: #fff;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  padding: 3rem;
  box-shadow: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
}

.page-module__OSLHOG__title {
  text-align: center;
  color: #1f2937;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: bold;
}

.page-module__OSLHOG__description {
  text-align: center;
  color: #6b7280;
  margin-bottom: 2rem;
  font-size: 1.125rem;
}

.page-module__OSLHOG__form {
  margin-bottom: 3rem;
}

.page-module__OSLHOG__formGroup {
  margin-bottom: 1.5rem;
}

.page-module__OSLHOG__label {
  color: #374151;
  margin-bottom: .5rem;
  font-weight: 500;
  display: block;
}

.page-module__OSLHOG__input, .page-module__OSLHOG__textarea {
  box-sizing: border-box;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  width: 100%;
  padding: .75rem;
  font-size: 1rem;
  transition: border-color .2s, box-shadow .2s;
}

.page-module__OSLHOG__input:focus, .page-module__OSLHOG__textarea:focus {
  border-color: #667eea;
  outline: none;
  box-shadow: 0 0 0 3px #667eea1a;
}

.page-module__OSLHOG__textarea {
  resize: vertical;
  min-height: 120px;
}

.page-module__OSLHOG__submitButton {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  width: 100%;
  padding: .875rem;
  font-size: 1rem;
  font-weight: 500;
  transition: transform .2s, box-shadow .2s;
}

.page-module__OSLHOG__submitButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -2px #0000000d;
}

.page-module__OSLHOG__contactInfo {
  background: #f9fafb;
  border-radius: 8px;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 2rem;
  display: grid;
}

@media (width >= 640px) {
  .page-module__OSLHOG__contactInfo {
    grid-template-columns: 1fr 1fr;
  }
}

.page-module__OSLHOG__infoItem {
  text-align: center;
}

.page-module__OSLHOG__infoTitle {
  color: #1f2937;
  margin-bottom: .5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.page-module__OSLHOG__infoText {
  color: #6b7280;
}

.page-module__OSLHOG__backLink {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  align-items: center;
  padding: .75rem 1.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: transform .2s, box-shadow .2s;
  display: inline-flex;
}

.page-module__OSLHOG__backLink:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -2px #0000000d;
}


/*# sourceMappingURL=src_app_contact_page_module_0b25142c.css.map*/