import styles from "./page.module.css";

export default function Contact() {
  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <h1 className={styles.title}>Contact Us</h1>
        <p className={styles.description}>
          Get in touch with us. We'd love to hear from you!
        </p>
        
        <form className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="name" className={styles.label}>
              Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              className={styles.input}
              placeholder="Your name"
              required
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="email" className={styles.label}>
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              className={styles.input}
              placeholder="<EMAIL>"
              required
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="message" className={styles.label}>
              Message
            </label>
            <textarea
              id="message"
              name="message"
              rows={5}
              className={styles.textarea}
              placeholder="Your message here..."
              required
            />
          </div>
          
          <button type="submit" className={styles.submitButton}>
            Send Message
          </button>
        </form>
        
        <div className={styles.contactInfo}>
          <div className={styles.infoItem}>
            <h3 className={styles.infoTitle}>Email</h3>
            <p className={styles.infoText}><EMAIL></p>
          </div>
          <div className={styles.infoItem}>
            <h3 className={styles.infoTitle}>Phone</h3>
            <p className={styles.infoText}>+****************</p>
          </div>
        </div>
        
        <a href="/" className={styles.backLink}>
          ← Back to Home
        </a>
      </main>
    </div>
  );
}
