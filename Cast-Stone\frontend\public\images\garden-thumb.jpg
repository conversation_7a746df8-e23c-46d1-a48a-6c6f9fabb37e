<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="garden" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#90EE90;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#D2B48C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B4513;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="400" fill="url(#garden)"/>
  <circle cx="300" cy="200" r="80" fill="rgba(255,255,255,0.2)"/>
  <text x="300" y="190" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">Garden Features</text>
  <text x="300" y="220" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="rgba(255,255,255,0.9)">Fountains & Planters</text>
</svg>
