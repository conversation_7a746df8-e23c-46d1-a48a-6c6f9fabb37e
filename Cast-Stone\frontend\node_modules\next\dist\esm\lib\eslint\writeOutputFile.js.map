{"version": 3, "sources": ["../../../src/lib/eslint/writeOutputFile.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport path from 'path'\nimport * as Log from '../../build/output/log'\nimport isError from '../../lib/is-error'\n\n/**\n * Check if a given file path is a directory or not.\n * Returns `true` if the path is a directory.\n */\nfunction isDirectory(\n  /**  The path to a file to check. */\n  filePath: string\n): Promise<boolean> {\n  return fs\n    .stat(filePath)\n    .then((stat) => stat.isDirectory())\n    .catch((error) => {\n      if (\n        isError(error) &&\n        (error.code === 'ENOENT' || error.code === 'ENOTDIR')\n      ) {\n        return false\n      }\n      throw error\n    })\n}\n/**\n * Create a file with eslint output data\n */\nexport async function writeOutputFile(\n  /** The name file that needs to be created */\n  outputFile: string,\n  /** The data that needs to be inserted into the file */\n  outputData: string\n): Promise<void> {\n  const filePath = path.resolve(process.cwd(), outputFile)\n\n  if (await isDirectory(filePath)) {\n    Log.error(\n      `Cannot write to output file path, it is a directory: ${filePath}`\n    )\n  } else {\n    try {\n      await fs.mkdir(path.dirname(filePath), { recursive: true })\n      await fs.writeFile(filePath, outputData)\n      Log.info(`The output file has been created: ${filePath}`)\n    } catch (err) {\n      Log.error(`There was a problem writing the output file: ${filePath}`)\n      console.error(err)\n    }\n  }\n}\n"], "names": ["promises", "fs", "path", "Log", "isError", "isDirectory", "filePath", "stat", "then", "catch", "error", "code", "writeOutputFile", "outputFile", "outputData", "resolve", "process", "cwd", "mkdir", "dirname", "recursive", "writeFile", "info", "err", "console"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,UAAU,OAAM;AACvB,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,aAAa,qBAAoB;AAExC;;;CAGC,GACD,SAASC,YACP,kCAAkC,GAClCC,QAAgB;IAEhB,OAAOL,GACJM,IAAI,CAACD,UACLE,IAAI,CAAC,CAACD,OAASA,KAAKF,WAAW,IAC/BI,KAAK,CAAC,CAACC;QACN,IACEN,QAAQM,UACPA,CAAAA,MAAMC,IAAI,KAAK,YAAYD,MAAMC,IAAI,KAAK,SAAQ,GACnD;YACA,OAAO;QACT;QACA,MAAMD;IACR;AACJ;AACA;;CAEC,GACD,OAAO,eAAeE,gBACpB,2CAA2C,GAC3CC,UAAkB,EAClB,qDAAqD,GACrDC,UAAkB;IAElB,MAAMR,WAAWJ,KAAKa,OAAO,CAACC,QAAQC,GAAG,IAAIJ;IAE7C,IAAI,MAAMR,YAAYC,WAAW;QAC/BH,IAAIO,KAAK,CACP,CAAC,qDAAqD,EAAEJ,UAAU;IAEtE,OAAO;QACL,IAAI;YACF,MAAML,GAAGiB,KAAK,CAAChB,KAAKiB,OAAO,CAACb,WAAW;gBAAEc,WAAW;YAAK;YACzD,MAAMnB,GAAGoB,SAAS,CAACf,UAAUQ;YAC7BX,IAAImB,IAAI,CAAC,CAAC,kCAAkC,EAAEhB,UAAU;QAC1D,EAAE,OAAOiB,KAAK;YACZpB,IAAIO,KAAK,CAAC,CAAC,6CAA6C,EAAEJ,UAAU;YACpEkB,QAAQd,KAAK,CAACa;QAChB;IACF;AACF"}