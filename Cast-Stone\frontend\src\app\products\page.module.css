/* Products Page Styles */
:root {
  --primary-color: #8B4513;
  --secondary-color: #D2B48C;
  --accent-color: #CD853F;
  --text-dark: #2C1810;
  --text-light: #6B5B4F;
  --background-light: #FAF7F2;
  --white: #FFFFFF;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.container {
  min-height: 100vh;
  background: var(--background-light);
  color: var(--text-dark);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Navigation */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.navContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  letter-spacing: -0.02em;
}

.logo span {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.navMenu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.navMenu a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.navMenu a:hover,
.navMenu a.active {
  color: var(--primary-color);
}

.navMenu a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.navMenu a:hover::after,
.navMenu a.active::after {
  width: 100%;
}

/* Hero Section */
.hero {
  padding: 8rem 2rem 4rem;
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
}

.heroSubtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  line-height: 1.7;
}

/* Products Section */
.productsSection {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.categoryFilter {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.categoryBtn {
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--primary-color);
  background: transparent;
  color: var(--primary-color);
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.categoryBtn:hover,
.categoryBtn.active {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.productCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.productCard:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
}

.productImageContainer {
  position: relative;
  overflow: hidden;
}

.productImage {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productCard:hover .productImage {
  transform: scale(1.05);
}

.productOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.productCard:hover .productOverlay {
  opacity: 1;
}

.viewBtn,
.cartBtn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.viewBtn {
  background: white;
  color: var(--primary-color);
}

.cartBtn {
  background: var(--primary-color);
  color: white;
}

.viewBtn:hover,
.cartBtn:hover {
  transform: translateY(-2px);
}

.productInfo {
  padding: 1.5rem;
}

.productName {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.productDescription {
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.productFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.productPrice {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.inquireBtn {
  padding: 0.5rem 1rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.inquireBtn:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
}

/* CTA Section */
.ctaSection {
  padding: 4rem 2rem;
  background: var(--primary-color);
  color: white;
  text-align: center;
}

.ctaContent h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.ctaContent p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.ctaBtn {
  padding: 1rem 2rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ctaBtn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Footer */
.footer {
  background: var(--text-dark);
  color: white;
  padding: 3rem 2rem 1rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footerSection h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footerSection p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footerSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerSection ul li {
  margin-bottom: 0.5rem;
}

.footerSection ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footerSection ul li a:hover {
  color: var(--secondary-color);
}

.footerBottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .navMenu {
    display: none;
  }
  
  .categoryFilter {
    gap: 0.5rem;
  }
  
  .categoryBtn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .productsGrid {
    grid-template-columns: 1fr;
  }
}
