"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const [currentCategory, setCurrentCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentProduct, setCurrentProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentTestimonial, setCurrentTestimonial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const categories = [\n        {\n            id: 1,\n            title: \"Architectural Designs\",\n            description: \"Professional architectural cast stone elements\",\n            image: \"/images/architectural-collection.jpg\"\n        },\n        {\n            id: 2,\n            title: \"Designer Products\",\n            description: \"Luxury designer cast stone pieces\",\n            image: \"/images/fireplace-collection.jpg\"\n        },\n        {\n            id: 3,\n            title: \"Limited Edition Products\",\n            description: \"Exclusive limited edition collections\",\n            image: \"/images/garden-collection.jpg\"\n        },\n        {\n            id: 4,\n            title: \"Sealer Maintenance Program\",\n            description: \"Professional maintenance and sealing services\",\n            image: \"/images/hero-cast-stone.svg\"\n        }\n    ];\n    const featuredProducts = [\n        {\n            id: 1,\n            name: \"Classic Fireplace Mantel\",\n            price: \"$2,500\",\n            image: \"/images/fireplace-collection.jpg\",\n            description: \"Handcrafted traditional mantel\"\n        },\n        {\n            id: 2,\n            name: \"Garden Fountain\",\n            price: \"$1,800\",\n            image: \"/images/garden-collection.jpg\",\n            description: \"Three-tier fountain\"\n        },\n        {\n            id: 3,\n            name: \"Classical Columns\",\n            price: \"$1,200\",\n            image: \"/images/architectural-collection.jpg\",\n            description: \"Corinthian style columns\"\n        }\n    ];\n    const testimonials = [\n        {\n            id: 1,\n            name: \"Sarah Johnson\",\n            company: \"Johnson Architecture\",\n            text: \"Cast Stone's architectural elements transformed our project. The quality and craftsmanship are unmatched.\",\n            rating: 5\n        },\n        {\n            id: 2,\n            name: \"Michael Chen\",\n            company: \"Elite Homes\",\n            text: \"We've been using Cast Stone products for over 10 years. Their consistency and beauty never disappoint.\",\n            rating: 5\n        },\n        {\n            id: 3,\n            name: \"Emma Rodriguez\",\n            company: \"Rodriguez Design Studio\",\n            text: \"The limited edition pieces add such elegance to our high-end residential projects.\",\n            rating: 5\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const categoryInterval = setInterval({\n                \"Home.useEffect.categoryInterval\": ()=>{\n                    setCurrentCategory({\n                        \"Home.useEffect.categoryInterval\": (prev)=>(prev + 1) % categories.length\n                    }[\"Home.useEffect.categoryInterval\"]);\n                }\n            }[\"Home.useEffect.categoryInterval\"], 4000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(categoryInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        categories.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const productInterval = setInterval({\n                \"Home.useEffect.productInterval\": ()=>{\n                    setCurrentProduct({\n                        \"Home.useEffect.productInterval\": (prev)=>(prev + 1) % featuredProducts.length\n                    }[\"Home.useEffect.productInterval\"]);\n                }\n            }[\"Home.useEffect.productInterval\"], 5000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(productInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        featuredProducts.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const testimonialInterval = setInterval({\n                \"Home.useEffect.testimonialInterval\": ()=>{\n                    setCurrentTestimonial({\n                        \"Home.useEffect.testimonialInterval\": (prev)=>(prev + 1) % testimonials.length\n                    }[\"Home.useEffect.testimonialInterval\"]);\n                }\n            }[\"Home.useEffect.testimonialInterval\"], 6000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(testimonialInterval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        testimonials.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().navigation),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().navContainer),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    children: \"Cast Stone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Interiors & Decorations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().navMenu),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/contact\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/about\",\n                                                        children: \"Our Story\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/retail-locator\",\n                                                        children: \"Retail Locator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/wholesale-signup\",\n                                                        children: \"Wholesale Sign-up\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/architectural\",\n                                                        children: \"Architectural Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/designer\",\n                                                        children: \"Designer Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/limited-edition\",\n                                                        children: \"Limited Edition Designs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/products/sealers\",\n                                                        children: \"Cast Stone Sealers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/collections\",\n                                        children: \"Collections\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/projects\",\n                                        children: \"Completed Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/catalog\",\n                                                        children: \"Catalog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/finishes\",\n                                                        children: \"Finishes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/videos\",\n                                                        children: \"Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/technical\",\n                                                        children: \"Technical Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/faqs\",\n                                                        children: \"FAQs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().hero),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().videoBackground),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                autoPlay: true,\n                                muted: true,\n                                loop: true,\n                                playsInline: true,\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroVideo),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: \"/herosection.mp4\",\n                                        type: \"video/mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Your browser does not support the video tag.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().videoOverlay)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroContent),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTitle),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().highlight),\n                                    children: \" Timeless Elegance in Cast Stone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSubtitle),\n                                children: \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroActions),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryBtn),\n                                        children: \"Explore Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryBtn),\n                                        children: \"Watch Our Story\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoriesSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().sectionHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Our Collections\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Explore our diverse range of cast stone products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoriesCarousel),\n                        children: categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categorySlide), \" \").concat(index === currentCategory ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryCard),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryImage),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: category.image,\n                                                alt: category.title,\n                                                width: 400,\n                                                height: 300,\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryImg)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryInfo),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: category.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: category.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryBtn),\n                                                    children: \"Explore\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryControls),\n                        children: categories.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().categoryDot), \" \").concat(index === currentCategory ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().activeDot) : ''),\n                                onClick: ()=>setCurrentCategory(index)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogContainer),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogContent),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogText),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Explore Our Complete Catalog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Browse through our comprehensive collection of cast stone products. From architectural elements to decorative pieces, find everything you need to transform your space with timeless elegance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogBtn),\n                                        children: \"View Catalog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/images/catalog-preview.jpg\",\n                                alt: \"Cast Stone Catalog\",\n                                width: 600,\n                                height: 400,\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().catalogImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().featuredProducts),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().sectionHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"This Week's Featured Products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Handpicked selections showcasing our finest craftsmanship\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCarousel),\n                        children: featuredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productSlide), \" \").concat(index === currentProduct ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCard),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImageContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: product.image,\n                                                    alt: product.name,\n                                                    width: 400,\n                                                    height: 300,\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImage)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productOverlay),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickViewBtn),\n                                                        children: \"Quick View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: product.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productFooter),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productPrice),\n                                                            children: product.price\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().inquireBtn),\n                                                            children: \"Inquire\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            }, product.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productControls),\n                        children: featuredProducts.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productDot), \" \").concat(index === currentProduct ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().activeDot) : ''),\n                                onClick: ()=>setCurrentProduct(index)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialsSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().sectionHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"What Our Clients Say\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Hear from professionals who trust Cast Stone for their projects\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialsCarousel),\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialSlide), \" \").concat(index === currentTestimonial ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialCard),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().stars),\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().star),\n                                                        children: \"★\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialText),\n                                                children: [\n                                                    '\"',\n                                                    testimonial.text,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialAuthor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: testimonial.company\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this)\n                            }, testimonial.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialControls),\n                        children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().testimonialDot), \" \").concat(index === currentTestimonial ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().activeDot) : ''),\n                                onClick: ()=>setCurrentTestimonial(index)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerContent),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Cast Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Creating timeless beauty with handcrafted cast stone elements for over 25 years.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/contact\",\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/about\",\n                                                    children: \"Our Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/retail-locator\",\n                                                    children: \"Retail Locator\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/wholesale-signup\",\n                                                    children: \"Wholesale Sign-up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/architectural\",\n                                                    children: \"Architectural Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/designer\",\n                                                    children: \"Designer Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/limited-edition\",\n                                                    children: \"Limited Edition\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/sealers\",\n                                                    children: \"Cast Stone Sealers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Discover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/catalog\",\n                                                    children: \"Catalog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections\",\n                                                    children: \"Collections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/projects\",\n                                                    children: \"Completed Projects\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/videos\",\n                                                    children: \"Videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/faqs\",\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Contact Info\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"123 Artisan Way\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 31\n                                            }, this),\n                                            \"Craftsman City, CC 12345\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Phone: (*************\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Email: <EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().footerBottom),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Cast Stone Interiors. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"zMjsSn4HrDFMzD+viBxakPhvDxI=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});