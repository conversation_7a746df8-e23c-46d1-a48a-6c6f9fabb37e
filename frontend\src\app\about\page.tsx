import styles from "./page.module.css";

export default function About() {
  return (
    <div className={styles.container}>
      <main className={styles.main}>
        <h1 className={styles.title}>About Us</h1>
        <p className={styles.description}>
          This is the about page demonstrating the modular CSS structure.
          Each page has its own TypeScript file and corresponding CSS module.
        </p>
        <div className={styles.content}>
          <section className={styles.section}>
            <h2 className={styles.sectionTitle}>Our Mission</h2>
            <p className={styles.sectionText}>
              We are committed to building amazing web applications with modern
              technologies and best practices.
            </p>
          </section>
          <section className={styles.section}>
            <h2 className={styles.sectionTitle}>Our Team</h2>
            <p className={styles.sectionText}>
              A dedicated team of developers passionate about creating
              exceptional user experiences.
            </p>
          </section>
        </div>
        <a href="/" className={styles.backLink}>
          ← Back to Home
        </a>
      </main>
    </div>
  );
}
