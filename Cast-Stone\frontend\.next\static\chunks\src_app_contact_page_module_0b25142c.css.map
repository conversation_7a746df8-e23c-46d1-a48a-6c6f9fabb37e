{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/contact/page.module.css"], "sourcesContent": ["/* Contact Page Styles */\r\n\r\n.container {\r\n  min-height: 100vh;\r\n  background: var(--background-light);\r\n  color: var(--text-dark);\r\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\r\n}\r\n\r\n/* Navigation */\r\n.navigation {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 1000;\r\n  padding: 1rem 0;\r\n  border-bottom: 1px solid rgba(139, 69, 19, 0.1);\r\n}\r\n\r\n.navContainer {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo h1 {\r\n  font-size: 1.8rem;\r\n  font-weight: 700;\r\n  color: var(--primary-color);\r\n  margin: 0;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.logo span {\r\n  font-size: 0.9rem;\r\n  color: var(--text-light);\r\n  font-weight: 400;\r\n}\r\n\r\n.navMenu {\r\n  display: flex;\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  gap: 2rem;\r\n}\r\n\r\n.navMenu a {\r\n  text-decoration: none;\r\n  color: var(--text-dark);\r\n  font-weight: 500;\r\n  transition: color 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.navMenu a:hover,\r\n.navMenu a.active {\r\n  color: var(--primary-color);\r\n}\r\n\r\n.navMenu a::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -5px;\r\n  left: 0;\r\n  width: 0;\r\n  height: 2px;\r\n  background: var(--primary-color);\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.navMenu a:hover::after,\r\n.navMenu a.active::after {\r\n  width: 100%;\r\n}\r\n\r\n/* Hero Section */\r\n.hero {\r\n  padding: 8rem 2rem 4rem;\r\n  text-align: center;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\r\n  color: white;\r\n}\r\n\r\n.heroContent {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.heroTitle {\r\n  font-size: 3.5rem;\r\n  font-weight: 800;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.heroSubtitle {\r\n  font-size: 1.2rem;\r\n  opacity: 0.9;\r\n  line-height: 1.7;\r\n}\r\n\r\n/* Contact Section */\r\n.contactSection {\r\n  padding: 6rem 2rem;\r\n}\r\n\r\n.contactContainer {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  align-items: start;\r\n}\r\n\r\n@media (max-width: 968px) {\r\n  .contactContainer {\r\n    grid-template-columns: 1fr;\r\n    gap: 3rem;\r\n  }\r\n}\r\n\r\n/* Form Container */\r\n.formContainer {\r\n  background: white;\r\n  padding: 3rem;\r\n  border-radius: 15px;\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n.formContainer h2 {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--text-dark);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.formContainer p {\r\n  color: var(--text-light);\r\n  margin-bottom: 2rem;\r\n  line-height: 1.6;\r\n}\r\n\r\n.form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.formRow {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 1rem;\r\n}\r\n\r\n@media (max-width: 640px) {\r\n  .formRow {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n.formGroup {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.label {\r\n  font-weight: 600;\r\n  color: var(--text-dark);\r\n  margin-bottom: 0.5rem;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.input,\r\n.textarea,\r\n.select {\r\n  padding: 1rem;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  transition: all 0.3s ease;\r\n  font-family: inherit;\r\n}\r\n\r\n.input:focus,\r\n.textarea:focus,\r\n.select:focus {\r\n  outline: none;\r\n  border-color: var(--primary-color);\r\n  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);\r\n}\r\n\r\n.textarea {\r\n  resize: vertical;\r\n  min-height: 120px;\r\n}\r\n\r\n.select {\r\n  cursor: pointer;\r\n}\r\n\r\n.submitButton {\r\n  padding: 1rem 2rem;\r\n  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.submitButton:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n/* Contact Info */\r\n.contactInfo {\r\n  background: var(--background-light);\r\n  padding: 3rem;\r\n  border-radius: 15px;\r\n}\r\n\r\n.contactInfo h2 {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: var(--text-dark);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.contactInfo > p {\r\n  color: var(--text-light);\r\n  margin-bottom: 2rem;\r\n  line-height: 1.6;\r\n}\r\n\r\n.infoGrid {\r\n  display: grid;\r\n  gap: 2rem;\r\n}\r\n\r\n.infoItem {\r\n  display: flex;\r\n  gap: 1rem;\r\n  align-items: flex-start;\r\n  padding: 1.5rem;\r\n  background: white;\r\n  border-radius: 10px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.infoItem:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow);\r\n}\r\n\r\n.infoIcon {\r\n  font-size: 1.5rem;\r\n  min-width: 40px;\r\n  text-align: center;\r\n}\r\n\r\n.infoTitle {\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: var(--text-dark);\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.infoText {\r\n  color: var(--text-light);\r\n  line-height: 1.6;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.infoSubtext {\r\n  color: var(--text-light);\r\n  font-size: 0.9rem;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* Footer */\r\n.footer {\r\n  background: var(--text-dark);\r\n  color: white;\r\n  padding: 3rem 2rem 1rem;\r\n}\r\n\r\n.footerContent {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.footerSection h3 {\r\n  font-size: 1.3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerSection h4 {\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerSection p {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  line-height: 1.6;\r\n}\r\n\r\n.footerSection ul {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.footerSection ul li {\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.footerSection ul li a {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-decoration: none;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.footerSection ul li a:hover {\r\n  color: var(--secondary-color);\r\n}\r\n\r\n.footerBottom {\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  padding-top: 1rem;\r\n  text-align: center;\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .heroTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .navMenu {\r\n    display: none;\r\n  }\r\n\r\n  .formContainer,\r\n  .contactInfo {\r\n    padding: 2rem;\r\n  }\r\n\r\n  .contactContainer {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .formContainer h2,\r\n  .contactInfo h2 {\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAEA;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AAQA;;;;AAKA;;;;;;;;;;;AAWA;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;;;;;;AASA;EACE;;;;;;AAOF;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;EACE;;;;;AAKF;;;;;AAKA;;;;;;;AAOA;;;;;;;;;AAWA;;;;;;AAQA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA", "debugId": null}}]}