{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20web/Cast-Stone/frontend/src/app/about/page.module.css"], "sourcesContent": ["/* About page specific styles */\n.container {\n  min-height: 100vh;\n  padding: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.main {\n  max-width: 800px;\n  background: white;\n  border-radius: 12px;\n  padding: 3rem;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  text-align: center;\n}\n\n.title {\n  font-size: 3rem;\n  font-weight: bold;\n  margin-bottom: 1.5rem;\n  color: #1f2937;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.description {\n  font-size: 1.25rem;\n  color: #6b7280;\n  margin-bottom: 3rem;\n  line-height: 1.6;\n}\n\n.content {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 2rem;\n  margin-bottom: 3rem;\n}\n\n@media (min-width: 768px) {\n  .content {\n    grid-template-columns: 1fr 1fr;\n  }\n}\n\n.section {\n  padding: 2rem;\n  background: #f9fafb;\n  border-radius: 8px;\n  border-left: 4px solid #667eea;\n}\n\n.sectionTitle {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1f2937;\n  margin-bottom: 1rem;\n}\n\n.sectionText {\n  color: #4b5563;\n  line-height: 1.6;\n}\n\n.backLink {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  text-decoration: none;\n  border-radius: 6px;\n  font-weight: 500;\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.backLink:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;AAOA;EACE;;;;;AAKF;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;AAYA", "debugId": null}}]}