{"version": 3, "sources": ["../../../src/lib/memory/startup.ts"], "sourcesContent": ["import v8 from 'v8'\nimport { info, warn } from '../../build/output/log'\nimport { italic } from '../picocolors'\nimport { startObservingGc } from './gc-observer'\nimport { startPeriodicMemoryUsageTracing } from './trace'\n\nexport function enableMemoryDebuggingMode(): void {\n  // This will generate a heap snapshot when the program is close to the\n  // memory limit. It does not give any warning to the user though which\n  // can be jarring. If memory is large, this may take a long time.\n  if ('setHeapSnapshotNearHeapLimit' in v8) {\n    v8.setHeapSnapshotNearHeapLimit(1)\n  }\n\n  // This flag will kill the process when it starts to GC thrash when it's\n  // close to the memory limit rather than continuing to try to collect\n  // memory ineffectively.\n  v8.setFlagsFromString('--detect-ineffective-gcs-near-heap-limit')\n\n  // This allows users to generate a heap snapshot on demand just by sending\n  // a signal to the process.\n  process.on('SIGUSR2', () => {\n    warn(\n      `Received SIGUSR2 signal. Generating heap snapshot. ${italic(\n        'Note: this will take some time.'\n      )}`\n    )\n    v8.writeHeapSnapshot()\n  })\n\n  startObservingGc()\n  startPeriodicMemoryUsageTracing()\n\n  warn(\n    `Memory debugging mode is enabled. ${italic(\n      'Note: This will affect performance.'\n    )}`\n  )\n  info(\n    ' - Heap snapshots will be automatically generated when the process reaches more than 70% of the memory limit and again when the process is just about to run out of memory.'\n  )\n  info(\n    ` - To manually generate a heap snapshot, send the process a SIGUSR2 signal: \\`kill -SIGUSR2 ${process.pid}\\``\n  )\n  info(\n    ' - Heap snapshots when there is high memory will take a very long time to complete and may be difficult to analyze in tools.'\n  )\n  info(\n    ' - See https://nextjs.org/docs/app/building-your-application/optimizing/memory-usage for more information.'\n  )\n}\n"], "names": ["v8", "info", "warn", "italic", "startObservingGc", "startPeriodicMemoryUsageTracing", "enableMemoryDebuggingMode", "setHeapSnapshotNearHeapLimit", "setFlagsFromString", "process", "on", "writeHeapSnapshot", "pid"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,SAASC,IAAI,EAAEC,IAAI,QAAQ,yBAAwB;AACnD,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,gBAAgB,QAAQ,gBAAe;AAChD,SAASC,+BAA+B,QAAQ,UAAS;AAEzD,OAAO,SAASC;IACd,sEAAsE;IACtE,sEAAsE;IACtE,iEAAiE;IACjE,IAAI,kCAAkCN,IAAI;QACxCA,GAAGO,4BAA4B,CAAC;IAClC;IAEA,wEAAwE;IACxE,qEAAqE;IACrE,wBAAwB;IACxBP,GAAGQ,kBAAkB,CAAC;IAEtB,0EAA0E;IAC1E,2BAA2B;IAC3BC,QAAQC,EAAE,CAAC,WAAW;QACpBR,KACE,CAAC,mDAAmD,EAAEC,OACpD,oCACC;QAELH,GAAGW,iBAAiB;IACtB;IAEAP;IACAC;IAEAH,KACE,CAAC,kCAAkC,EAAEC,OACnC,wCACC;IAELF,KACE;IAEFA,KACE,CAAC,4FAA4F,EAAEQ,QAAQG,GAAG,CAAC,EAAE,CAAC;IAEhHX,KACE;IAEFA,KACE;AAEJ"}