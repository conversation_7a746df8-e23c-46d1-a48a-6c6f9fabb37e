/* [project]/src/app/contact/page.module.css [app-client] (css) */
:root {
  --primary-color: #8b4513;
  --secondary-color: tan;
  --accent-color: peru;
  --text-dark: #2c1810;
  --text-light: #6b5b4f;
  --background-light: #faf7f2;
  --white: #fff;
  --shadow: 0 10px 30px #0000001a;
  --shadow-hover: 0 20px 40px #00000026;
}

.page-module__OSLHOG__container {
  background: var(--background-light);
  min-height: 100vh;
  color: var(--text-dark);
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
}

.page-module__OSLHOG__navigation {
  backdrop-filter: blur(10px);
  z-index: 1000;
  background: #fffffff2;
  border-bottom: 1px solid #8b45131a;
  padding: 1rem 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.page-module__OSLHOG__navContainer {
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
}

.page-module__OSLHOG__logo h1 {
  color: var(--primary-color);
  letter-spacing: -.02em;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-module__OSLHOG__logo span {
  color: var(--text-light);
  font-size: .9rem;
  font-weight: 400;
}

.page-module__OSLHOG__navMenu {
  gap: 2rem;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.page-module__OSLHOG__navMenu a {
  color: var(--text-dark);
  font-weight: 500;
  text-decoration: none;
  transition: color .3s;
  position: relative;
}

.page-module__OSLHOG__navMenu a:hover, .page-module__OSLHOG__navMenu a.page-module__OSLHOG__active {
  color: var(--primary-color);
}

.page-module__OSLHOG__navMenu a:after {
  content: "";
  background: var(--primary-color);
  width: 0;
  height: 2px;
  transition: width .3s;
  position: absolute;
  bottom: -5px;
  left: 0;
}

.page-module__OSLHOG__navMenu a:hover:after, .page-module__OSLHOG__navMenu a.page-module__OSLHOG__active:after {
  width: 100%;
}

.page-module__OSLHOG__hero {
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: #fff;
  padding: 8rem 2rem 4rem;
}

.page-module__OSLHOG__heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.page-module__OSLHOG__heroTitle {
  margin-bottom: 1.5rem;
  font-size: 3.5rem;
  font-weight: 800;
}

.page-module__OSLHOG__heroSubtitle {
  opacity: .9;
  font-size: 1.2rem;
  line-height: 1.7;
}

.page-module__OSLHOG__contactSection {
  padding: 6rem 2rem;
}

.page-module__OSLHOG__contactContainer {
  grid-template-columns: 1fr 1fr;
  align-items: start;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

@media (width <= 968px) {
  .page-module__OSLHOG__contactContainer {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

.page-module__OSLHOG__formContainer {
  box-shadow: var(--shadow);
  background: #fff;
  border-radius: 15px;
  padding: 3rem;
}

.page-module__OSLHOG__formContainer h2 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 2rem;
  font-weight: 700;
}

.page-module__OSLHOG__formContainer p {
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.page-module__OSLHOG__form {
  flex-direction: column;
  gap: 1.5rem;
  display: flex;
}

.page-module__OSLHOG__formRow {
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  display: grid;
}

@media (width <= 640px) {
  .page-module__OSLHOG__formRow {
    grid-template-columns: 1fr;
  }
}

.page-module__OSLHOG__formGroup {
  flex-direction: column;
  display: flex;
}

.page-module__OSLHOG__label {
  color: var(--text-dark);
  margin-bottom: .5rem;
  font-size: .9rem;
  font-weight: 600;
}

.page-module__OSLHOG__input, .page-module__OSLHOG__textarea, .page-module__OSLHOG__select {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  font-family: inherit;
  font-size: 1rem;
  transition: all .3s;
}

.page-module__OSLHOG__input:focus, .page-module__OSLHOG__textarea:focus, .page-module__OSLHOG__select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px #8b45131a;
}

.page-module__OSLHOG__textarea {
  resize: vertical;
  min-height: 120px;
}

.page-module__OSLHOG__select {
  cursor: pointer;
}

.page-module__OSLHOG__submitButton {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: #fff;
  cursor: pointer;
  border: none;
  border-radius: 8px;
  margin-top: 1rem;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module__OSLHOG__submitButton:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.page-module__OSLHOG__contactInfo {
  background: var(--background-light);
  border-radius: 15px;
  padding: 3rem;
}

.page-module__OSLHOG__contactInfo h2 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 2rem;
  font-weight: 700;
}

.page-module__OSLHOG__contactInfo > p {
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.page-module__OSLHOG__infoGrid {
  gap: 2rem;
  display: grid;
}

.page-module__OSLHOG__infoItem {
  background: #fff;
  border-radius: 10px;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  transition: all .3s;
  display: flex;
  box-shadow: 0 5px 15px #0000000d;
}

.page-module__OSLHOG__infoItem:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.page-module__OSLHOG__infoIcon {
  text-align: center;
  min-width: 40px;
  font-size: 1.5rem;
}

.page-module__OSLHOG__infoTitle {
  color: var(--text-dark);
  margin-bottom: .5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.page-module__OSLHOG__infoText {
  color: var(--text-light);
  margin-bottom: .25rem;
  line-height: 1.6;
}

.page-module__OSLHOG__infoSubtext {
  color: var(--text-light);
  opacity: .8;
  font-size: .9rem;
}

.page-module__OSLHOG__footer {
  background: var(--text-dark);
  color: #fff;
  padding: 3rem 2rem 1rem;
}

.page-module__OSLHOG__footerContent {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto 2rem;
  display: grid;
}

.page-module__OSLHOG__footerSection h3 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.page-module__OSLHOG__footerSection h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.page-module__OSLHOG__footerSection p {
  color: #fffc;
  line-height: 1.6;
}

.page-module__OSLHOG__footerSection ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__OSLHOG__footerSection ul li {
  margin-bottom: .5rem;
}

.page-module__OSLHOG__footerSection ul li a {
  color: #fffc;
  text-decoration: none;
  transition: color .3s;
}

.page-module__OSLHOG__footerSection ul li a:hover {
  color: var(--secondary-color);
}

.page-module__OSLHOG__footerBottom {
  text-align: center;
  color: #fff9;
  border-top: 1px solid #ffffff1a;
  padding-top: 1rem;
}

@media (width <= 768px) {
  .page-module__OSLHOG__heroTitle {
    font-size: 2.5rem;
  }

  .page-module__OSLHOG__navMenu {
    display: none;
  }

  .page-module__OSLHOG__formContainer, .page-module__OSLHOG__contactInfo {
    padding: 2rem;
  }

  .page-module__OSLHOG__contactContainer {
    padding: 0 1rem;
  }

  .page-module__OSLHOG__formContainer h2, .page-module__OSLHOG__contactInfo h2 {
    font-size: 1.5rem;
  }
}


/*# sourceMappingURL=src_app_contact_page_module_0b25142c.css.map*/