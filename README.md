# Patrick's Web - Full Stack Application

A modern full-stack web application built with Node.js backend and Next.js frontend with TypeScript and modular CSS architecture.

## 🏗️ Project Structure

```
Patricks web/
├── backend/                 # Node.js Express server
│   ├── controllers/         # Route controllers
│   ├── middleware/          # Custom middleware
│   ├── models/             # Database models
│   ├── routes/             # API routes
│   ├── .env                # Environment variables
│   ├── .env.example        # Environment template
│   ├── server.js           # Main server file
│   └── package.json        # Backend dependencies
├── frontend/               # Next.js React application
│   ├── src/
│   │   └── app/           # App router pages
│   │       ├── about/     # About page with modular CSS
│   │       ├── contact/   # Contact page with modular CSS
│   │       ├── page.tsx   # Home page
│   │       └── page.module.css # Home page styles
│   ├── public/            # Static assets
│   └── package.json       # Frontend dependencies
└── README.md              # This file
```

## 🚀 Features

- **Backend**: Node.js with Express.js framework
- **Frontend**: Next.js 14+ with TypeScript and App Router
- **Styling**: Modular CSS architecture (each page has its own .module.css file)
- **Database**: MongoDB with Mongoose ODM
- **Environment**: Configurable environment variables
- **Development**: Hot reload for both frontend and backend

## 📋 Prerequisites

Before running this project, make sure you have the following installed:

- [Node.js](https://nodejs.org/) (v18 or higher)
- [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)
- [MongoDB](https://www.mongodb.com/) (local installation or MongoDB Atlas)

## 🛠️ Installation & Setup

### 1. Clone the repository
```bash
git clone <repository-url>
cd "Patricks web"
```

### 2. Backend Setup
```bash
cd backend
npm install
```

### 3. Environment Configuration
Copy the environment template and configure your variables:
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/patricks-web
JWT_SECRET=your-super-secret-jwt-key
FRONTEND_URL=http://localhost:3000
```

### 4. Frontend Setup
```bash
cd ../frontend
npm install
```

## 🏃‍♂️ Running the Application

### Development Mode

#### Start Backend Server
```bash
cd backend
npm run dev
```
The backend will run on http://localhost:5000

#### Start Frontend Server
```bash
cd frontend
npm run dev
```
The frontend will run on http://localhost:3000

### Production Mode

#### Backend
```bash
cd backend
npm start
```

#### Frontend
```bash
cd frontend
npm run build
npm start
```

## 🎨 Modular CSS Architecture

This project uses a modular CSS approach where each page has its own CSS module:

- `page.tsx` - TypeScript component file
- `page.module.css` - Corresponding CSS module file

### Example Structure:
```
src/app/about/
├── page.tsx           # About page component
└── page.module.css    # About page styles

src/app/contact/
├── page.tsx           # Contact page component
└── page.module.css    # Contact page styles
```

### Benefits:
- ✅ Scoped styles (no CSS conflicts)
- ✅ Better maintainability
- ✅ Component-specific styling
- ✅ Easier debugging
- ✅ Better performance (only loads required CSS)

## 📡 API Endpoints

### Base URL: `http://localhost:5000/api`

- `GET /health` - Health check endpoint
- `GET /test` - Test endpoint

## 🗄️ Database

The application uses MongoDB with Mongoose ODM. The connection string is configured in the `.env` file.

### Local MongoDB
```env
MONGODB_URI=mongodb://localhost:27017/patricks-web
```

### MongoDB Atlas
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/database-name
```

## 🔧 Available Scripts

### Backend Scripts
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server

### Frontend Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the ISC License.
