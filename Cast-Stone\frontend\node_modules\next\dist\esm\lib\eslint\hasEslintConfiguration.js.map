{"version": 3, "sources": ["../../../src/lib/eslint/hasEslintConfiguration.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\n\nexport type ConfigAvailable = {\n  exists: boolean\n  emptyEslintrc?: boolean\n  emptyPkgJsonConfig?: boolean\n  firstTimeSetup?: true\n}\n\nexport async function hasEslintConfiguration(\n  eslintrcFile: string | null,\n  packageJsonConfig: { eslintConfig: any } | null\n): Promise<ConfigAvailable> {\n  const configObject = {\n    exists: false,\n    emptyEslintrc: false,\n    emptyPkgJsonConfig: false,\n  }\n\n  if (eslintrcFile) {\n    const content = await fs.readFile(eslintrcFile, { encoding: 'utf8' }).then(\n      (txt) => txt.trim().replace(/\\n/g, ''),\n      () => null\n    )\n\n    if (\n      content === '' ||\n      content === '{}' ||\n      content === '---' ||\n      content === 'module.exports = {}'\n    ) {\n      configObject.emptyEslintrc = true\n    } else {\n      configObject.exists = true\n    }\n  } else if (packageJsonConfig?.eslintConfig) {\n    if (Object.keys(packageJsonConfig.eslintConfig).length) {\n      configObject.exists = true\n    } else {\n      configObject.emptyPkgJsonConfig = true\n    }\n  }\n  return configObject\n}\n"], "names": ["promises", "fs", "hasEslintConfiguration", "eslintrcFile", "packageJsonConfig", "configObject", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "content", "readFile", "encoding", "then", "txt", "trim", "replace", "eslintConfig", "Object", "keys", "length"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AASnC,OAAO,eAAeC,uBACpBC,YAA2B,EAC3BC,iBAA+C;IAE/C,MAAMC,eAAe;QACnBC,QAAQ;QACRC,eAAe;QACfC,oBAAoB;IACtB;IAEA,IAAIL,cAAc;QAChB,MAAMM,UAAU,MAAMR,GAAGS,QAAQ,CAACP,cAAc;YAAEQ,UAAU;QAAO,GAAGC,IAAI,CACxE,CAACC,MAAQA,IAAIC,IAAI,GAAGC,OAAO,CAAC,OAAO,KACnC,IAAM;QAGR,IACEN,YAAY,MACZA,YAAY,QACZA,YAAY,SACZA,YAAY,uBACZ;YACAJ,aAAaE,aAAa,GAAG;QAC/B,OAAO;YACLF,aAAaC,MAAM,GAAG;QACxB;IACF,OAAO,IAAIF,qCAAAA,kBAAmBY,YAAY,EAAE;QAC1C,IAAIC,OAAOC,IAAI,CAACd,kBAAkBY,YAAY,EAAEG,MAAM,EAAE;YACtDd,aAAaC,MAAM,GAAG;QACxB,OAAO;YACLD,aAAaG,kBAAkB,GAAG;QACpC;IACF;IACA,OAAOH;AACT"}