/* [project]/src/app/page.module.css [app-client] (css) */
.page-module___8aEwW__container {
  background: var(--background-light);
  min-height: 100vh;
  color: var(--text-dark);
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
}

.page-module___8aEwW__navigation {
  backdrop-filter: blur(10px);
  z-index: 1000;
  background: #fffffff2;
  border-bottom: 1px solid #8b45131a;
  padding: 1rem 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.page-module___8aEwW__navContainer {
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
}

.page-module___8aEwW__logo h1 {
  color: var(--primary-color);
  letter-spacing: -.02em;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-module___8aEwW__logo span {
  color: var(--text-light);
  font-size: .9rem;
  font-weight: 400;
}

.page-module___8aEwW__navMenu {
  gap: 2rem;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.page-module___8aEwW__navMenu a {
  color: var(--text-dark);
  font-weight: 500;
  text-decoration: none;
  transition: color .3s;
  position: relative;
}

.page-module___8aEwW__navMenu a:hover {
  color: var(--primary-color);
}

.page-module___8aEwW__navMenu a:after {
  content: "";
  background: var(--primary-color);
  width: 0;
  height: 2px;
  transition: width .3s;
  position: absolute;
  bottom: -5px;
  left: 0;
}

.page-module___8aEwW__navMenu a:hover:after {
  width: 100%;
}

.page-module___8aEwW__hero {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 4rem;
  max-width: 1200px;
  min-height: 80vh;
  margin: 0 auto;
  padding: 8rem 2rem 4rem;
  display: grid;
}

@media (width <= 768px) {
  .page-module___8aEwW__hero {
    text-align: center;
    grid-template-columns: 1fr;
    padding: 6rem 1rem 3rem;
  }
}

.page-module___8aEwW__heroContent {
  max-width: 500px;
}

.page-module___8aEwW__heroTitle {
  color: var(--text-dark);
  margin-bottom: 1.5rem;
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
}

.page-module___8aEwW__highlight {
  color: var(--primary-color);
  position: relative;
}

.page-module___8aEwW__highlight:after {
  content: "";
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  width: 100%;
  height: 3px;
  position: absolute;
  bottom: 0;
  left: 0;
}

.page-module___8aEwW__heroSubtitle {
  color: var(--text-light);
  margin-bottom: 2.5rem;
  font-size: 1.2rem;
  line-height: 1.7;
}

.page-module___8aEwW__heroActions {
  flex-wrap: wrap;
  gap: 1rem;
  display: flex;
}

.page-module___8aEwW__primaryBtn {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: #fff;
  cursor: pointer;
  box-shadow: var(--shadow);
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module___8aEwW__primaryBtn:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

.page-module___8aEwW__secondaryBtn {
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  cursor: pointer;
  background: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module___8aEwW__secondaryBtn:hover {
  background: var(--primary-color);
  color: #fff;
  transform: translateY(-2px);
}

.page-module___8aEwW__heroImage {
  box-shadow: var(--shadow);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.page-module___8aEwW__heroImg {
  object-fit: cover;
  width: 100%;
  height: auto;
  transition: transform .3s;
}

.page-module___8aEwW__heroImage:hover .page-module___8aEwW__heroImg {
  transform: scale(1.05);
}

.page-module___8aEwW__sectionHeader {
  text-align: center;
  max-width: 600px;
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
}

.page-module___8aEwW__sectionHeader h2 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module___8aEwW__sectionHeader p {
  color: var(--text-light);
  font-size: 1.1rem;
}

.page-module___8aEwW__videoSection {
  background: #fff;
  padding: 6rem 2rem;
  overflow: hidden;
}

.page-module___8aEwW__videoCarousel {
  justify-content: center;
  align-items: center;
  max-width: 1000px;
  height: 500px;
  margin: 0 auto;
  display: flex;
  position: relative;
}

.page-module___8aEwW__videoSlide {
  opacity: 0;
  pointer-events: none;
  width: 100%;
  transition: all .8s cubic-bezier(.4, 0, .2, 1);
  position: absolute;
  transform: translateX(100px);
}

.page-module___8aEwW__videoSlide.page-module___8aEwW__active {
  opacity: 1;
  pointer-events: all;
  transform: translateX(0);
}

.page-module___8aEwW__slideFromLeft {
  transform: translateX(-100px);
}

.page-module___8aEwW__slideFromLeft.page-module___8aEwW__active {
  transform: translateX(0);
}

.page-module___8aEwW__slideFromRight {
  transform: translateX(100px);
}

.page-module___8aEwW__slideFromRight.page-module___8aEwW__active {
  transform: translateX(0);
}

.page-module___8aEwW__videoContainer {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 3rem;
  max-width: 900px;
  margin: 0 auto;
  display: grid;
}

@media (width <= 768px) {
  .page-module___8aEwW__videoContainer {
    text-align: center;
    grid-template-columns: 1fr;
  }
}

.page-module___8aEwW__videoPlaceholder {
  box-shadow: var(--shadow);
  cursor: pointer;
  border-radius: 15px;
  transition: transform .3s;
  position: relative;
  overflow: hidden;
}

.page-module___8aEwW__videoPlaceholder:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-5px);
}

.page-module___8aEwW__videoThumbnail {
  object-fit: cover;
  width: 100%;
  height: 300px;
}

.page-module___8aEwW__playButton {
  transition: transform .3s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.page-module___8aEwW__videoPlaceholder:hover .page-module___8aEwW__playButton {
  transform: translate(-50%, -50%)scale(1.1);
}

.page-module___8aEwW__videoInfo h3 {
  color: var(--text-dark);
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.page-module___8aEwW__videoInfo p {
  color: var(--text-light);
  font-size: 1.1rem;
  line-height: 1.6;
}

.page-module___8aEwW__videoControls {
  justify-content: center;
  gap: 1rem;
  margin-top: 3rem;
  display: flex;
}

.page-module___8aEwW__videoDot {
  cursor: pointer;
  background: #8b45134d;
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s;
}

.page-module___8aEwW__videoDot.page-module___8aEwW__activeDot {
  background: var(--primary-color);
  transform: scale(1.2);
}

.page-module___8aEwW__videoDot:hover {
  background: var(--primary-color);
}

.page-module___8aEwW__featuredProducts {
  background: var(--background-light);
  padding: 6rem 2rem;
}

.page-module___8aEwW__productGrid {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.page-module___8aEwW__productCard {
  box-shadow: var(--shadow);
  cursor: pointer;
  background: #fff;
  border-radius: 15px;
  transition: all .3s;
  overflow: hidden;
}

.page-module___8aEwW__productCard:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-10px);
}

.page-module___8aEwW__productImage {
  object-fit: cover;
  width: 100%;
  height: 250px;
}

.page-module___8aEwW__productInfo {
  padding: 1.5rem;
}

.page-module___8aEwW__productInfo h3 {
  color: var(--text-dark);
  margin-bottom: .5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.page-module___8aEwW__productInfo p {
  color: var(--text-light);
  margin-bottom: 1rem;
}

.page-module___8aEwW__productPrice {
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 700;
}

.page-module___8aEwW__newsletter {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: #fff;
  text-align: center;
  padding: 4rem 2rem;
}

.page-module___8aEwW__newsletterContent h2 {
  margin-bottom: 1rem;
  font-size: 2.2rem;
  font-weight: 700;
}

.page-module___8aEwW__newsletterContent p {
  opacity: .9;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.page-module___8aEwW__newsletterForm {
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
  display: flex;
}

@media (width <= 640px) {
  .page-module___8aEwW__newsletterForm {
    flex-direction: column;
  }
}

.page-module___8aEwW__emailInput {
  border: none;
  border-radius: 50px;
  outline: none;
  flex: 1;
  padding: 1rem 1.5rem;
  font-size: 1rem;
}

.page-module___8aEwW__subscribeBtn {
  color: var(--primary-color);
  cursor: pointer;
  white-space: nowrap;
  background: #fff;
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.page-module___8aEwW__subscribeBtn:hover {
  background: var(--background-light);
  transform: translateY(-2px);
}

.page-module___8aEwW__footer {
  background: var(--text-dark);
  color: #fff;
  padding: 3rem 2rem 1rem;
}

.page-module___8aEwW__footerContent {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto 2rem;
  display: grid;
}

.page-module___8aEwW__footerSection h3 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 700;
}

.page-module___8aEwW__footerSection h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.page-module___8aEwW__footerSection p {
  color: #fffc;
  line-height: 1.6;
}

.page-module___8aEwW__footerSection ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module___8aEwW__footerSection ul li {
  margin-bottom: .5rem;
}

.page-module___8aEwW__footerSection ul li a {
  color: #fffc;
  text-decoration: none;
  transition: color .3s;
}

.page-module___8aEwW__footerSection ul li a:hover {
  color: var(--secondary-color);
}

.page-module___8aEwW__footerBottom {
  text-align: center;
  color: #fff9;
  border-top: 1px solid #ffffff1a;
  padding-top: 1rem;
}

@media (width <= 768px) {
  .page-module___8aEwW__heroTitle {
    font-size: 2.5rem;
  }

  .page-module___8aEwW__sectionHeader h2 {
    font-size: 2rem;
  }

  .page-module___8aEwW__navMenu {
    display: none;
  }

  .page-module___8aEwW__videoCarousel {
    height: auto;
  }

  .page-module___8aEwW__videoSlide {
    opacity: 1;
    position: relative;
    transform: none;
  }

  .page-module___8aEwW__videoSlide.page-module___8aEwW__active {
    display: block;
  }

  .page-module___8aEwW__videoSlide:not(.page-module___8aEwW__active) {
    display: none;
  }
}


/*# sourceMappingURL=src_app_page_module_d12eb71c.css.map*/