/* [project]/src/app/about/page.module.css [app-client] (css) */
.page-module__NfDiEG__container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  display: flex;
}

.page-module__NfDiEG__main {
  text-align: center;
  background: #fff;
  border-radius: 12px;
  max-width: 800px;
  padding: 3rem;
  box-shadow: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
}

.page-module__NfDiEG__title {
  color: #1f2937;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-bottom: 1.5rem;
  font-size: 3rem;
  font-weight: bold;
}

.page-module__NfDiEG__description {
  color: #6b7280;
  margin-bottom: 3rem;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__NfDiEG__content {
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
  display: grid;
}

@media (width >= 768px) {
  .page-module__NfDiEG__content {
    grid-template-columns: 1fr 1fr;
  }
}

.page-module__NfDiEG__section {
  background: #f9fafb;
  border-left: 4px solid #667eea;
  border-radius: 8px;
  padding: 2rem;
}

.page-module__NfDiEG__sectionTitle {
  color: #1f2937;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-module__NfDiEG__sectionText {
  color: #4b5563;
  line-height: 1.6;
}

.page-module__NfDiEG__backLink {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  align-items: center;
  padding: .75rem 1.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: transform .2s, box-shadow .2s;
  display: inline-flex;
}

.page-module__NfDiEG__backLink:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -2px #0000000d;
}


/*# sourceMappingURL=src_app_about_page_module_c798c172.css.map*/